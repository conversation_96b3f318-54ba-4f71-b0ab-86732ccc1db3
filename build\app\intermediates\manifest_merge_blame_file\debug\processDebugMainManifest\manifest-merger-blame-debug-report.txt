1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.khatwa.khatwa"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:41:13-72
25-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:42:13-50
27-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29        <intent>
29-->[:file_picker] D:\Development\ Yassine\flutter\Khotwa\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
30            <action android:name="android.intent.action.GET_CONTENT" />
30-->[:file_picker] D:\Development\ Yassine\flutter\Khotwa\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
30-->[:file_picker] D:\Development\ Yassine\flutter\Khotwa\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
31
32            <data android:mimeType="*/*" />
32-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:42:13-50
32-->D:\Development\ Yassine\flutter\Khotwa\android\app\src\main\AndroidManifest.xml:42:19-48
33        </intent> <!-- Needs to be explicitly declared on Android R+ -->
34        <package android:name="com.google.android.apps.maps" />
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
34-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
35    </queries>
36
37    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
37-->[:location] D:\Development\ Yassine\flutter\Khotwa\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
37-->[:location] D:\Development\ Yassine\flutter\Khotwa\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-78
38    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- Include required permissions for Google Maps API to run. -->
38-->[:location] D:\Development\ Yassine\flutter\Khotwa\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
38-->[:location] D:\Development\ Yassine\flutter\Khotwa\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-76
39    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
39-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
39-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:22-76
40
41    <uses-feature
41-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
42        android:glEsVersion="0x00020000"
42-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
43        android:required="true" />
43-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
44
45    <permission
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
46        android:name="com.khatwa.khatwa.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.khatwa.khatwa.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
50
51    <application
52        android:name="android.app.Application"
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
54        android:debuggable="true"
55        android:extractNativeLibs="true"
56        android:icon="@mipmap/ic_launcher"
57        android:label="khatwa" >
58        <activity
59            android:name="com.khatwa.khatwa.MainActivity"
60            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
61            android:exported="true"
62            android:hardwareAccelerated="true"
63            android:launchMode="singleTop"
64            android:taskAffinity=""
65            android:theme="@style/LaunchTheme"
66            android:windowSoftInputMode="adjustResize" >
67
68            <!--
69                 Specifies an Android theme to apply to this Activity as soon as
70                 the Android process has started. This theme is visible to the user
71                 while the Flutter UI initializes. After that, this theme continues
72                 to determine the Window background behind the Flutter UI.
73            -->
74            <meta-data
75                android:name="io.flutter.embedding.android.NormalTheme"
76                android:resource="@style/NormalTheme" />
77
78            <intent-filter>
79                <action android:name="android.intent.action.MAIN" />
80
81                <category android:name="android.intent.category.LAUNCHER" />
82            </intent-filter>
83        </activity>
84        <!--
85             Don't delete the meta-data below.
86             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
87        -->
88        <meta-data
89            android:name="flutterEmbedding"
90            android:value="2" />
91
92        <service
92-->[:location] D:\Development\ Yassine\flutter\Khotwa\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-15:56
93            android:name="com.lyokone.location.FlutterLocationService"
93-->[:location] D:\Development\ Yassine\flutter\Khotwa\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-71
94            android:enabled="true"
94-->[:location] D:\Development\ Yassine\flutter\Khotwa\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-35
95            android:exported="false"
95-->[:location] D:\Development\ Yassine\flutter\Khotwa\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-37
96            android:foregroundServiceType="location" />
96-->[:location] D:\Development\ Yassine\flutter\Khotwa\build\location\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-53
97        <service
97-->[:geolocator_android] D:\Development\ Yassine\flutter\Khotwa\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
98            android:name="com.baseflow.geolocator.GeolocatorLocationService"
98-->[:geolocator_android] D:\Development\ Yassine\flutter\Khotwa\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
99            android:enabled="true"
99-->[:geolocator_android] D:\Development\ Yassine\flutter\Khotwa\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
100            android:exported="false"
100-->[:geolocator_android] D:\Development\ Yassine\flutter\Khotwa\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
101            android:foregroundServiceType="location" />
101-->[:geolocator_android] D:\Development\ Yassine\flutter\Khotwa\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
102
103        <provider
103-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
104            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
104-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
105            android:authorities="com.khatwa.khatwa.flutter.image_provider"
105-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
106            android:exported="false"
106-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
107            android:grantUriPermissions="true" >
107-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
108            <meta-data
108-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
109                android:name="android.support.FILE_PROVIDER_PATHS"
109-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
110                android:resource="@xml/flutter_image_picker_file_paths" />
110-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
111        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
112        <service
112-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
113            android:name="com.google.android.gms.metadata.ModuleDependencies"
113-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
114            android:enabled="false"
114-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
115            android:exported="false" >
115-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
116            <intent-filter>
116-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
117                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
117-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
117-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
118            </intent-filter>
119
120            <meta-data
120-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
121                android:name="photopicker_activity:0:required"
121-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
122                android:value="" />
122-->[:image_picker_android] D:\Development\ Yassine\flutter\Khotwa\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
123        </service>
124
125        <activity
125-->[:url_launcher_android] D:\Development\ Yassine\flutter\Khotwa\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
126            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
126-->[:url_launcher_android] D:\Development\ Yassine\flutter\Khotwa\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
127            android:exported="false"
127-->[:url_launcher_android] D:\Development\ Yassine\flutter\Khotwa\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
128            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Needs to be explicitly declared on P+ -->
128-->[:url_launcher_android] D:\Development\ Yassine\flutter\Khotwa\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
129        <uses-library
129-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
130            android:name="org.apache.http.legacy"
130-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
131            android:required="false" />
131-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
132
133        <activity
133-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
134            android:name="com.google.android.gms.common.api.GoogleApiActivity"
134-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
135            android:exported="false"
135-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
136            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
136-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
137
138        <meta-data
138-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
139            android:name="com.google.android.gms.version"
139-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
140            android:value="@integer/google_play_services_version" />
140-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
141
142        <uses-library
142-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
143            android:name="androidx.window.extensions"
143-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
144            android:required="false" />
144-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
145        <uses-library
145-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
146            android:name="androidx.window.sidecar"
146-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
147            android:required="false" />
147-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
148
149        <provider
149-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
150            android:name="androidx.startup.InitializationProvider"
150-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
151            android:authorities="com.khatwa.khatwa.androidx-startup"
151-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
152            android:exported="false" >
152-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
153            <meta-data
153-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
154                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
154-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
155                android:value="androidx.startup" />
155-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
156            <meta-data
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
157                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
158                android:value="androidx.startup" />
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
159        </provider>
160
161        <receiver
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
162            android:name="androidx.profileinstaller.ProfileInstallReceiver"
162-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
163            android:directBootAware="false"
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
164            android:enabled="true"
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
165            android:exported="true"
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
166            android:permission="android.permission.DUMP" >
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
167            <intent-filter>
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
168                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
169            </intent-filter>
170            <intent-filter>
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
171                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
172            </intent-filter>
173            <intent-filter>
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
174                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
175            </intent-filter>
176            <intent-filter>
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
177                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
178            </intent-filter>
179        </receiver>
180    </application>
181
182</manifest>
