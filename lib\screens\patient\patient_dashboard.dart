import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/common/gradient_button.dart';
import '../../widgets/common/loading_widget.dart';

class PatientDashboard extends StatefulWidget {
  const PatientDashboard({Key? key}) : super(key: key);

  @override
  State<PatientDashboard> createState() => _PatientDashboardState();
}

class _PatientDashboardState extends State<PatientDashboard> {
  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final patient = authProvider.patient;
        final user = authProvider.user;

        if (patient == null || user == null) {
          return const Scaffold(
            body: Center(
              child: PatientLoadingWidget(message: 'جاري تحميل البيانات...'),
            ),
          );
        }

        return Scaffold(
          body: CustomScrollView(
            slivers: [
              // App Bar
              SliverAppBar(
                expandedHeight: 200,
                floating: false,
                pinned: true,
                backgroundColor: AppColors.patientPrimary,
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    'مرحباً، ${user.fullName.split(' ').first}',
                    style: AppTheme.heading6.copyWith(color: AppColors.white),
                  ),
                  background: Container(
                    decoration: const BoxDecoration(
                      gradient: AppColors.patientGradient,
                    ),
                    child: SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                CircleAvatar(
                                  radius: 30,
                                  backgroundColor: AppColors.white,
                                  child: Text(
                                    user.fullName.substring(0, 1),
                                    style: AppTheme.heading4.copyWith(
                                      color: AppColors.patientPrimary,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        user.fullName,
                                        style: AppTheme.heading5.copyWith(
                                          color: AppColors.white,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'العمر: ${patient.age} سنة',
                                        style: AppTheme.bodyMedium.copyWith(
                                          color: AppColors.white.withOpacity(
                                            0.9,
                                          ),
                                        ),
                                      ),
                                      Text(
                                        AppConstants.disabilityTypes[patient
                                                .primaryDisability
                                                .toString()
                                                .split('.')
                                                .last] ??
                                            '',
                                        style: AppTheme.bodySmall.copyWith(
                                          color: AppColors.white.withOpacity(
                                            0.8,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 40),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.notifications_outlined),
                    onPressed: () => _showComingSoon('الإشعارات'),
                  ),
                ],
              ),

              // Content
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Quick Stats
                      _buildQuickStats(),
                      const SizedBox(height: 24),

                      // Quick Actions
                      Text('الإجراءات السريعة', style: AppTheme.heading5),
                      const SizedBox(height: 16),
                      _buildQuickActions(),
                      const SizedBox(height: 24),

                      // Recent Activity
                      Text('النشاط الأخير', style: AppTheme.heading5),
                      const SizedBox(height: 16),
                      _buildRecentActivity(),
                      const SizedBox(height: 24),

                      // Health Tips
                      Text('نصائح صحية', style: AppTheme.heading5),
                      const SizedBox(height: 16),
                      _buildHealthTips(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            icon: Icons.medical_services,
            title: 'الاستشارات',
            value: '0',
            subtitle: 'استشارة نشطة',
            color: AppColors.info,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            icon: Icons.chat,
            title: 'المحادثات',
            value: '0',
            subtitle: 'محادثة جديدة',
            color: AppColors.success,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            icon: Icons.schedule,
            title: 'المواعيد',
            value: '0',
            subtitle: 'موعد قادم',
            color: AppColors.warning,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required String subtitle,
    required Color color,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _showComingSoon(title);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        child: Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(icon, color: color, size: 24),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    value,
                    style: AppTheme.heading4.copyWith(
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    title,
                    style: AppTheme.labelMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppTheme.caption.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    final actions = [
      _QuickAction(
        icon: Icons.add_circle_outline,
        title: 'استشارة جديدة',
        subtitle: 'احجز استشارة مع مختص',
        color: AppColors.patientPrimary,
        onTap: () => _showComingSoon('الاستشارات'),
      ),
      _QuickAction(
        icon: Icons.folder_shared,
        title: 'ملفي الطبي',
        subtitle: 'عرض وإدارة ملفك الطبي',
        color: AppColors.info,
        onTap: () => Navigator.pushNamed(context, '/patient/medical-file'),
      ),
      _QuickAction(
        icon: Icons.location_on,
        title: 'المراكز القريبة',
        subtitle: 'ابحث عن مراكز قريبة',
        color: AppColors.success,
        onTap: () => _showComingSoon('المراكز الطبية'),
      ),
      _QuickAction(
        icon: Icons.chat,
        title: 'المحادثات',
        subtitle: 'تواصل مع الأطباء',
        color: AppColors.warning,
        onTap: () => _showComingSoon('المحادثات'),
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.2,
      ),
      itemCount: actions.length,
      itemBuilder: (context, index) {
        final action = actions[index];
        return GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            action.onTap();
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            child: Card(
              elevation: 6,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      action.color.withOpacity(0.1),
                      action.color.withOpacity(0.05),
                    ],
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: action.color.withOpacity(0.15),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(action.icon, size: 24, color: action.color),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        action.title,
                        style: AppTheme.labelLarge.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        action.subtitle,
                        style: AppTheme.caption.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentActivity() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(Icons.history, size: 48, color: AppColors.grey400),
            const SizedBox(height: 16),
            Text(
              'لا يوجد نشاط حديث',
              style: AppTheme.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ستظهر هنا أحدث أنشطتك مثل الاستشارات والمواعيد',
              style: AppTheme.caption,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            PatientButton(
              text: 'ابدأ أول استشارة',
              onPressed: () => _showComingSoon('الاستشارات'),
              size: ButtonSize.small,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthTips() {
    final tips = [
      {
        'title': 'ممارسة الرياضة',
        'description': 'ممارسة التمارين المناسبة لحالتك يومياً',
        'icon': Icons.fitness_center,
      },
      {
        'title': 'التغذية الصحية',
        'description': 'تناول وجبات متوازنة وغنية بالفيتامينات',
        'icon': Icons.restaurant,
      },
      {
        'title': 'المتابعة الدورية',
        'description': 'احرص على المتابعة الدورية مع طبيبك',
        'icon': Icons.schedule,
      },
    ];

    return Column(
      children: tips.map((tip) {
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: AppColors.patientLight,
              child: Icon(
                tip['icon'] as IconData,
                color: AppColors.patientPrimary,
              ),
            ),
            title: Text(tip['title'] as String, style: AppTheme.labelLarge),
            subtitle: Text(
              tip['description'] as String,
              style: AppTheme.bodySmall,
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _showComingSoon('النصائح الصحية'),
          ),
        );
      }).toList(),
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('ميزة $feature ستكون متاحة قريباً'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

class _QuickAction {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback onTap;

  _QuickAction({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.onTap,
  });
}
