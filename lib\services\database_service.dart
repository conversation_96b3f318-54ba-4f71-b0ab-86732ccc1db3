import 'dart:async';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../models/patient_model.dart';
import '../models/doctor_model.dart';
import '../models/consultation_model.dart';
import '../models/chat_model.dart';
import '../models/medical_center_model.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, AppConstants.databaseName);

    return await openDatabase(
      path,
      version: AppConstants.databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Users table
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        email TEXT UNIQUE NOT NULL,
        fullName TEXT NOT NULL,
        phoneNumber TEXT,
        userType TEXT NOT NULL,
        status TEXT NOT NULL,
        profileImageUrl TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        additionalData TEXT
      )
    ''');

    // Patients table
    await db.execute('''
      CREATE TABLE patients (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        dateOfBirth TEXT NOT NULL,
        gender TEXT NOT NULL,
        nationalId TEXT,
        address TEXT NOT NULL,
        city TEXT NOT NULL,
        country TEXT NOT NULL,
        primaryDisability TEXT NOT NULL,
        secondaryDisabilities TEXT,
        medicalHistory TEXT,
        allergies TEXT,
        currentMedications TEXT,
        emergencyContact TEXT NOT NULL,
        insuranceProvider TEXT,
        insuranceNumber TEXT,
        additionalNotes TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (userId) REFERENCES users (id)
      )
    ''');

    // Doctors table
    await db.execute('''
      CREATE TABLE doctors (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        licenseNumber TEXT NOT NULL,
        primarySpecialization TEXT NOT NULL,
        secondarySpecializations TEXT,
        certifications TEXT,
        workExperience TEXT,
        bio TEXT,
        languages TEXT,
        clinicAddress TEXT,
        clinicPhone TEXT,
        workingHours TEXT,
        consultationFee REAL,
        isAvailableForChat INTEGER NOT NULL DEFAULT 1,
        isAvailableForConsultation INTEGER NOT NULL DEFAULT 1,
        rating REAL NOT NULL DEFAULT 0.0,
        totalReviews INTEGER NOT NULL DEFAULT 0,
        status TEXT NOT NULL,
        verifiedAt TEXT,
        verifiedBy TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (userId) REFERENCES users (id)
      )
    ''');

    // Consultations table
    await db.execute('''
      CREATE TABLE consultations (
        id TEXT PRIMARY KEY,
        patientId TEXT NOT NULL,
        doctorId TEXT,
        type TEXT NOT NULL,
        status TEXT NOT NULL,
        priority TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        symptoms TEXT,
        attachments TEXT,
        doctorNotes TEXT,
        diagnosis TEXT,
        treatmentPlan TEXT,
        prescriptions TEXT,
        scheduledAt TEXT,
        startedAt TEXT,
        completedAt TEXT,
        fee REAL,
        isPaid INTEGER NOT NULL DEFAULT 0,
        paymentId TEXT,
        additionalData TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (patientId) REFERENCES patients (id),
        FOREIGN KEY (doctorId) REFERENCES doctors (id)
      )
    ''');

    // Chats table
    await db.execute('''
      CREATE TABLE chats (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        consultationId TEXT,
        participantIds TEXT NOT NULL,
        title TEXT,
        description TEXT,
        lastMessageId TEXT,
        lastMessageAt TEXT,
        unreadCounts TEXT,
        status TEXT NOT NULL,
        metadata TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (consultationId) REFERENCES consultations (id)
      )
    ''');

    // Chat Messages table
    await db.execute('''
      CREATE TABLE chat_messages (
        id TEXT PRIMARY KEY,
        chatId TEXT NOT NULL,
        senderId TEXT NOT NULL,
        replyToMessageId TEXT,
        type TEXT NOT NULL,
        content TEXT NOT NULL,
        mediaUrl TEXT,
        fileName TEXT,
        fileSize INTEGER,
        status TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        editedAt TEXT,
        isDeleted INTEGER NOT NULL DEFAULT 0,
        metadata TEXT,
        FOREIGN KEY (chatId) REFERENCES chats (id),
        FOREIGN KEY (senderId) REFERENCES users (id)
      )
    ''');

    // Medical Centers table
    await db.execute('''
      CREATE TABLE medical_centers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        type TEXT NOT NULL,
        services TEXT NOT NULL,
        location TEXT NOT NULL,
        contactInfo TEXT NOT NULL,
        workingHours TEXT,
        imageUrls TEXT,
        certifications TEXT,
        insuranceAccepted TEXT,
        rating REAL NOT NULL DEFAULT 0.0,
        totalReviews INTEGER NOT NULL DEFAULT 0,
        reviews TEXT,
        isActive INTEGER NOT NULL DEFAULT 1,
        isVerified INTEGER NOT NULL DEFAULT 0,
        licenseNumber TEXT,
        verifiedAt TEXT,
        additionalInfo TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )
    ''');

    // Create indexes for better performance
    await db.execute('CREATE INDEX idx_users_email ON users (email)');
    await db.execute('CREATE INDEX idx_users_type ON users (userType)');
    await db.execute('CREATE INDEX idx_patients_user ON patients (userId)');
    await db.execute('CREATE INDEX idx_doctors_user ON doctors (userId)');
    await db.execute('CREATE INDEX idx_doctors_status ON doctors (status)');
    await db.execute('CREATE INDEX idx_consultations_patient ON consultations (patientId)');
    await db.execute('CREATE INDEX idx_consultations_doctor ON consultations (doctorId)');
    await db.execute('CREATE INDEX idx_consultations_status ON consultations (status)');
    await db.execute('CREATE INDEX idx_chats_participants ON chats (participantIds)');
    await db.execute('CREATE INDEX idx_messages_chat ON chat_messages (chatId)');
    await db.execute('CREATE INDEX idx_messages_timestamp ON chat_messages (timestamp)');
    await db.execute('CREATE INDEX idx_centers_type ON medical_centers (type)');
    await db.execute('CREATE INDEX idx_centers_active ON medical_centers (isActive)');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
    if (oldVersion < newVersion) {
      // Add migration logic for future versions
    }
  }

  // Generic CRUD operations
  Future<int> insert(String table, Map<String, dynamic> data) async {
    final db = await database;
    return await db.insert(table, data, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<List<Map<String, dynamic>>> query(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final db = await database;
    return await db.query(
      table,
      where: where,
      whereArgs: whereArgs,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
  }

  Future<int> update(
    String table,
    Map<String, dynamic> data, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    return await db.update(table, data, where: where, whereArgs: whereArgs);
  }

  Future<int> delete(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    return await db.delete(table, where: where, whereArgs: whereArgs);
  }

  // User operations
  Future<void> insertUser(User user) async {
    await insert('users', user.toJson());
  }

  Future<User?> getUserById(String id) async {
    final results = await query('users', where: 'id = ?', whereArgs: [id]);
    if (results.isEmpty) return null;
    return User.fromJson(results.first);
  }

  Future<User?> getUserByEmail(String email) async {
    final results = await query('users', where: 'email = ?', whereArgs: [email]);
    if (results.isEmpty) return null;
    return User.fromJson(results.first);
  }

  Future<List<User>> getUsersByType(UserType userType) async {
    final results = await query(
      'users',
      where: 'userType = ?',
      whereArgs: [userType.toString().split('.').last],
    );
    return results.map((json) => User.fromJson(json)).toList();
  }

  // Patient operations
  Future<void> insertPatient(Patient patient) async {
    final data = patient.toJson();
    // Convert complex objects to JSON strings
    data['secondaryDisabilities'] = patient.secondaryDisabilities
        .map((e) => e.toString().split('.').last)
        .join(',');
    data['medicalHistory'] = patient.medicalHistory
        .map((e) => e.toJson())
        .toString();
    data['allergies'] = patient.allergies.join(',');
    data['currentMedications'] = patient.currentMedications.join(',');
    data['emergencyContact'] = patient.emergencyContact.toJson().toString();
    
    await insert('patients', data);
  }

  Future<Patient?> getPatientByUserId(String userId) async {
    final results = await query('patients', where: 'userId = ?', whereArgs: [userId]);
    if (results.isEmpty) return null;
    return Patient.fromJson(results.first);
  }

  // Doctor operations
  Future<void> insertDoctor(Doctor doctor) async {
    final data = doctor.toJson();
    // Convert complex objects to JSON strings
    data['secondarySpecializations'] = doctor.secondarySpecializations
        .map((e) => e.toString().split('.').last)
        .join(',');
    data['certifications'] = doctor.certifications
        .map((e) => e.toJson())
        .toString();
    data['workExperience'] = doctor.workExperience
        .map((e) => e.toJson())
        .toString();
    data['languages'] = doctor.languages.join(',');
    data['workingHours'] = doctor.workingHours?.toString();
    
    await insert('doctors', data);
  }

  Future<Doctor?> getDoctorByUserId(String userId) async {
    final results = await query('doctors', where: 'userId = ?', whereArgs: [userId]);
    if (results.isEmpty) return null;
    return Doctor.fromJson(results.first);
  }

  Future<List<Doctor>> getVerifiedDoctors() async {
    final results = await query(
      'doctors',
      where: 'status = ?',
      whereArgs: ['verified'],
      orderBy: 'rating DESC',
    );
    return results.map((json) => Doctor.fromJson(json)).toList();
  }

  // Consultation operations
  Future<void> insertConsultation(Consultation consultation) async {
    final data = consultation.toJson();
    // Convert complex objects to JSON strings
    data['symptoms'] = consultation.symptoms.join(',');
    data['attachments'] = consultation.attachments
        .map((e) => e.toJson())
        .toString();
    data['prescriptions'] = consultation.prescriptions.join(',');
    
    await insert('consultations', data);
  }

  Future<List<Consultation>> getConsultationsByPatient(String patientId) async {
    final results = await query(
      'consultations',
      where: 'patientId = ?',
      whereArgs: [patientId],
      orderBy: 'createdAt DESC',
    );
    return results.map((json) => Consultation.fromJson(json)).toList();
  }

  Future<List<Consultation>> getConsultationsByDoctor(String doctorId) async {
    final results = await query(
      'consultations',
      where: 'doctorId = ?',
      whereArgs: [doctorId],
      orderBy: 'createdAt DESC',
    );
    return results.map((json) => Consultation.fromJson(json)).toList();
  }

  // Clear all data (for logout or reset)
  Future<void> clearAllData() async {
    final db = await database;
    await db.delete('chat_messages');
    await db.delete('chats');
    await db.delete('consultations');
    await db.delete('doctors');
    await db.delete('patients');
    await db.delete('users');
    await db.delete('medical_centers');
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
}
