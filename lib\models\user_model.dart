import 'package:uuid/uuid.dart';

enum UserType {
  patient,
  doctor,
  admin,
}

enum UserStatus {
  active,
  inactive,
  pending,
  suspended,
}

class User {
  final String id;
  final String email;
  final String fullName;
  final String? phoneNumber;
  final UserType userType;
  final UserStatus status;
  final String? profileImageUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? additionalData;

  User({
    String? id,
    required this.email,
    required this.fullName,
    this.phoneNumber,
    required this.userType,
    this.status = UserStatus.active,
    this.profileImageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.additionalData,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      email: json['email'],
      fullName: json['fullName'],
      phoneNumber: json['phoneNumber'],
      userType: UserType.values.firstWhere(
        (e) => e.toString() == 'UserType.${json['userType']}',
      ),
      status: UserStatus.values.firstWhere(
        (e) => e.toString() == 'UserStatus.${json['status']}',
      ),
      profileImageUrl: json['profileImageUrl'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      additionalData: json['additionalData'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'fullName': fullName,
      'phoneNumber': phoneNumber,
      'userType': userType.toString().split('.').last,
      'status': status.toString().split('.').last,
      'profileImageUrl': profileImageUrl,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'additionalData': additionalData,
    };
  }

  User copyWith({
    String? email,
    String? fullName,
    String? phoneNumber,
    UserType? userType,
    UserStatus? status,
    String? profileImageUrl,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalData,
  }) {
    return User(
      id: id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      userType: userType ?? this.userType,
      status: status ?? this.status,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      additionalData: additionalData ?? this.additionalData,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'User(id: $id, email: $email, fullName: $fullName, userType: $userType)';
  }
}
