import 'package:uuid/uuid.dart';

enum ConsultationType {
  general,
  followUp,
  emergency,
  secondOpinion,
}

enum ConsultationStatus {
  pending,
  accepted,
  inProgress,
  completed,
  cancelled,
  rejected,
}

enum ConsultationPriority {
  low,
  medium,
  high,
  urgent,
}

class ConsultationAttachment {
  final String id;
  final String fileName;
  final String fileUrl;
  final String fileType; // image, document, video, etc.
  final int fileSize;
  final DateTime uploadedAt;

  ConsultationAttachment({
    String? id,
    required this.fileName,
    required this.fileUrl,
    required this.fileType,
    required this.fileSize,
    DateTime? uploadedAt,
  })  : id = id ?? const Uuid().v4(),
        uploadedAt = uploadedAt ?? DateTime.now();

  factory ConsultationAttachment.fromJson(Map<String, dynamic> json) {
    return ConsultationAttachment(
      id: json['id'],
      fileName: json['fileName'],
      fileUrl: json['fileUrl'],
      fileType: json['fileType'],
      fileSize: json['fileSize'],
      uploadedAt: DateTime.parse(json['uploadedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileName': fileName,
      'fileUrl': fileUrl,
      'fileType': fileType,
      'fileSize': fileSize,
      'uploadedAt': uploadedAt.toIso8601String(),
    };
  }
}

class Consultation {
  final String id;
  final String patientId;
  final String? doctorId; // null if not assigned yet
  final ConsultationType type;
  final ConsultationStatus status;
  final ConsultationPriority priority;
  final String title;
  final String description;
  final List<String> symptoms;
  final List<ConsultationAttachment> attachments;
  final String? doctorNotes;
  final String? diagnosis;
  final String? treatmentPlan;
  final List<String> prescriptions;
  final DateTime? scheduledAt;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final double? fee;
  final bool isPaid;
  final String? paymentId;
  final Map<String, dynamic>? additionalData;
  final DateTime createdAt;
  final DateTime updatedAt;

  Consultation({
    String? id,
    required this.patientId,
    this.doctorId,
    required this.type,
    this.status = ConsultationStatus.pending,
    this.priority = ConsultationPriority.medium,
    required this.title,
    required this.description,
    this.symptoms = const [],
    this.attachments = const [],
    this.doctorNotes,
    this.diagnosis,
    this.treatmentPlan,
    this.prescriptions = const [],
    this.scheduledAt,
    this.startedAt,
    this.completedAt,
    this.fee,
    this.isPaid = false,
    this.paymentId,
    this.additionalData,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory Consultation.fromJson(Map<String, dynamic> json) {
    return Consultation(
      id: json['id'],
      patientId: json['patientId'],
      doctorId: json['doctorId'],
      type: ConsultationType.values.firstWhere(
        (e) => e.toString() == 'ConsultationType.${json['type']}',
      ),
      status: ConsultationStatus.values.firstWhere(
        (e) => e.toString() == 'ConsultationStatus.${json['status']}',
      ),
      priority: ConsultationPriority.values.firstWhere(
        (e) => e.toString() == 'ConsultationPriority.${json['priority']}',
      ),
      title: json['title'],
      description: json['description'],
      symptoms: List<String>.from(json['symptoms'] ?? []),
      attachments: (json['attachments'] as List<dynamic>?)
              ?.map((e) => ConsultationAttachment.fromJson(e))
              .toList() ??
          [],
      doctorNotes: json['doctorNotes'],
      diagnosis: json['diagnosis'],
      treatmentPlan: json['treatmentPlan'],
      prescriptions: List<String>.from(json['prescriptions'] ?? []),
      scheduledAt: json['scheduledAt'] != null
          ? DateTime.parse(json['scheduledAt'])
          : null,
      startedAt: json['startedAt'] != null
          ? DateTime.parse(json['startedAt'])
          : null,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'])
          : null,
      fee: json['fee']?.toDouble(),
      isPaid: json['isPaid'] ?? false,
      paymentId: json['paymentId'],
      additionalData: json['additionalData'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patientId': patientId,
      'doctorId': doctorId,
      'type': type.toString().split('.').last,
      'status': status.toString().split('.').last,
      'priority': priority.toString().split('.').last,
      'title': title,
      'description': description,
      'symptoms': symptoms,
      'attachments': attachments.map((e) => e.toJson()).toList(),
      'doctorNotes': doctorNotes,
      'diagnosis': diagnosis,
      'treatmentPlan': treatmentPlan,
      'prescriptions': prescriptions,
      'scheduledAt': scheduledAt?.toIso8601String(),
      'startedAt': startedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'fee': fee,
      'isPaid': isPaid,
      'paymentId': paymentId,
      'additionalData': additionalData,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  Duration? get duration {
    if (startedAt == null || completedAt == null) return null;
    return completedAt!.difference(startedAt!);
  }

  bool get isActive => [
        ConsultationStatus.pending,
        ConsultationStatus.accepted,
        ConsultationStatus.inProgress,
      ].contains(status);

  bool get isCompleted => status == ConsultationStatus.completed;

  bool get requiresPayment => fee != null && fee! > 0 && !isPaid;

  Consultation copyWith({
    String? patientId,
    String? doctorId,
    ConsultationType? type,
    ConsultationStatus? status,
    ConsultationPriority? priority,
    String? title,
    String? description,
    List<String>? symptoms,
    List<ConsultationAttachment>? attachments,
    String? doctorNotes,
    String? diagnosis,
    String? treatmentPlan,
    List<String>? prescriptions,
    DateTime? scheduledAt,
    DateTime? startedAt,
    DateTime? completedAt,
    double? fee,
    bool? isPaid,
    String? paymentId,
    Map<String, dynamic>? additionalData,
    DateTime? updatedAt,
  }) {
    return Consultation(
      id: id,
      patientId: patientId ?? this.patientId,
      doctorId: doctorId ?? this.doctorId,
      type: type ?? this.type,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      title: title ?? this.title,
      description: description ?? this.description,
      symptoms: symptoms ?? this.symptoms,
      attachments: attachments ?? this.attachments,
      doctorNotes: doctorNotes ?? this.doctorNotes,
      diagnosis: diagnosis ?? this.diagnosis,
      treatmentPlan: treatmentPlan ?? this.treatmentPlan,
      prescriptions: prescriptions ?? this.prescriptions,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      fee: fee ?? this.fee,
      isPaid: isPaid ?? this.isPaid,
      paymentId: paymentId ?? this.paymentId,
      additionalData: additionalData ?? this.additionalData,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Consultation && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Consultation(id: $id, patientId: $patientId, doctorId: $doctorId, status: $status)';
  }
}
