import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';

enum LoadingType {
  circular,
  linear,
  dots,
  pulse,
  shimmer,
}

class LoadingWidget extends StatelessWidget {
  final LoadingType type;
  final String? message;
  final Color? color;
  final double size;
  final bool overlay;

  const LoadingWidget({
    Key? key,
    this.type = LoadingType.circular,
    this.message,
    this.color,
    this.size = 50,
    this.overlay = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget loadingWidget = _buildLoadingWidget();

    if (message != null) {
      loadingWidget = Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          loadingWidget,
          const SizedBox(height: 16),
          Text(
            message!,
            style: AppTheme.bodyMedium.copyWith(
              color: color ?? AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    }

    if (overlay) {
      return Container(
        color: AppColors.black.withOpacity(0.5),
        child: Center(child: loadingWidget),
      );
    }

    return Center(child: loadingWidget);
  }

  Widget _buildLoadingWidget() {
    switch (type) {
      case LoadingType.circular:
        return SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? AppColors.patientPrimary,
            ),
            strokeWidth: 3,
          ),
        );

      case LoadingType.linear:
        return SizedBox(
          width: size * 2,
          child: LinearProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? AppColors.patientPrimary,
            ),
            backgroundColor: AppColors.grey200,
          ),
        );

      case LoadingType.dots:
        return _DotsLoadingWidget(
          color: color ?? AppColors.patientPrimary,
          size: size,
        );

      case LoadingType.pulse:
        return _PulseLoadingWidget(
          color: color ?? AppColors.patientPrimary,
          size: size,
        );

      case LoadingType.shimmer:
        return _ShimmerLoadingWidget(
          size: size,
        );
    }
  }
}

class _DotsLoadingWidget extends StatefulWidget {
  final Color color;
  final double size;

  const _DotsLoadingWidget({
    required this.color,
    required this.size,
  });

  @override
  State<_DotsLoadingWidget> createState() => _DotsLoadingWidgetState();
}

class _DotsLoadingWidgetState extends State<_DotsLoadingWidget>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(
      3,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    _startAnimations();
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 200), () {
        if (mounted) {
          _controllers[i].repeat(reverse: true);
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Container(
              margin: const EdgeInsets.symmetric(horizontal: 2),
              width: widget.size / 5,
              height: widget.size / 5,
              decoration: BoxDecoration(
                color: widget.color.withOpacity(0.3 + (_animations[index].value * 0.7)),
                shape: BoxShape.circle,
              ),
            );
          },
        );
      }),
    );
  }
}

class _PulseLoadingWidget extends StatefulWidget {
  final Color color;
  final double size;

  const _PulseLoadingWidget({
    required this.color,
    required this.size,
  });

  @override
  State<_PulseLoadingWidget> createState() => _PulseLoadingWidgetState();
}

class _PulseLoadingWidgetState extends State<_PulseLoadingWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.size,
          height: widget.size,
          decoration: BoxDecoration(
            color: widget.color.withOpacity(0.3 + (_animation.value * 0.7)),
            shape: BoxShape.circle,
          ),
        );
      },
    );
  }
}

class _ShimmerLoadingWidget extends StatefulWidget {
  final double size;

  const _ShimmerLoadingWidget({
    required this.size,
  });

  @override
  State<_ShimmerLoadingWidget> createState() => _ShimmerLoadingWidgetState();
}

class _ShimmerLoadingWidgetState extends State<_ShimmerLoadingWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.size * 2,
          height: widget.size / 2,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ],
              colors: const [
                AppColors.grey200,
                AppColors.grey100,
                AppColors.grey200,
              ],
            ),
          ),
        );
      },
    );
  }
}

// Specialized loading widgets
class PatientLoadingWidget extends StatelessWidget {
  final String? message;
  final LoadingType type;

  const PatientLoadingWidget({
    Key? key,
    this.message,
    this.type = LoadingType.circular,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LoadingWidget(
      type: type,
      message: message,
      color: AppColors.patientPrimary,
    );
  }
}

class DoctorLoadingWidget extends StatelessWidget {
  final String? message;
  final LoadingType type;

  const DoctorLoadingWidget({
    Key? key,
    this.message,
    this.type = LoadingType.circular,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LoadingWidget(
      type: type,
      message: message,
      color: AppColors.doctorPrimary,
    );
  }
}

class AdminLoadingWidget extends StatelessWidget {
  final String? message;
  final LoadingType type;

  const AdminLoadingWidget({
    Key? key,
    this.message,
    this.type = LoadingType.circular,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LoadingWidget(
      type: type,
      message: message,
      color: AppColors.adminPrimary,
    );
  }
}

// Full screen loading overlay
class LoadingOverlay extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final String? message;
  final Color? color;

  const LoadingOverlay({
    Key? key,
    required this.child,
    required this.isLoading,
    this.message,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          LoadingWidget(
            type: LoadingType.circular,
            message: message,
            color: color,
            overlay: true,
          ),
      ],
    );
  }
}
