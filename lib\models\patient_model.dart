import 'package:uuid/uuid.dart';

enum Gender {
  male,
  female,
  other,
}

enum DisabilityType {
  mobility,
  prosthetic,
  amputation,
  spinalCord,
  cerebralPalsy,
  other,
}

enum EmergencyContactRelation {
  parent,
  spouse,
  sibling,
  child,
  friend,
  caregiver,
  other,
}

class EmergencyContact {
  final String name;
  final String phoneNumber;
  final EmergencyContactRelation relation;
  final String? email;

  EmergencyContact({
    required this.name,
    required this.phoneNumber,
    required this.relation,
    this.email,
  });

  factory EmergencyContact.fromJson(Map<String, dynamic> json) {
    return EmergencyContact(
      name: json['name'],
      phoneNumber: json['phoneNumber'],
      relation: EmergencyContactRelation.values.firstWhere(
        (e) => e.toString() == 'EmergencyContactRelation.${json['relation']}',
      ),
      email: json['email'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phoneNumber': phoneNumber,
      'relation': relation.toString().split('.').last,
      'email': email,
    };
  }
}

class MedicalHistory {
  final String condition;
  final String? description;
  final DateTime? diagnosisDate;
  final String? treatment;
  final bool isActive;

  MedicalHistory({
    required this.condition,
    this.description,
    this.diagnosisDate,
    this.treatment,
    this.isActive = true,
  });

  factory MedicalHistory.fromJson(Map<String, dynamic> json) {
    return MedicalHistory(
      condition: json['condition'],
      description: json['description'],
      diagnosisDate: json['diagnosisDate'] != null
          ? DateTime.parse(json['diagnosisDate'])
          : null,
      treatment: json['treatment'],
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'condition': condition,
      'description': description,
      'diagnosisDate': diagnosisDate?.toIso8601String(),
      'treatment': treatment,
      'isActive': isActive,
    };
  }
}

class Patient {
  final String id;
  final String userId; // Reference to User model
  final DateTime dateOfBirth;
  final Gender gender;
  final String? nationalId;
  final String address;
  final String city;
  final String country;
  final DisabilityType primaryDisability;
  final List<DisabilityType> secondaryDisabilities;
  final List<MedicalHistory> medicalHistory;
  final List<String> allergies;
  final List<String> currentMedications;
  final EmergencyContact emergencyContact;
  final String? insuranceProvider;
  final String? insuranceNumber;
  final Map<String, dynamic>? additionalNotes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Patient({
    String? id,
    required this.userId,
    required this.dateOfBirth,
    required this.gender,
    this.nationalId,
    required this.address,
    required this.city,
    required this.country,
    required this.primaryDisability,
    this.secondaryDisabilities = const [],
    this.medicalHistory = const [],
    this.allergies = const [],
    this.currentMedications = const [],
    required this.emergencyContact,
    this.insuranceProvider,
    this.insuranceNumber,
    this.additionalNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory Patient.fromJson(Map<String, dynamic> json) {
    return Patient(
      id: json['id'],
      userId: json['userId'],
      dateOfBirth: DateTime.parse(json['dateOfBirth']),
      gender: Gender.values.firstWhere(
        (e) => e.toString() == 'Gender.${json['gender']}',
      ),
      nationalId: json['nationalId'],
      address: json['address'],
      city: json['city'],
      country: json['country'],
      primaryDisability: DisabilityType.values.firstWhere(
        (e) => e.toString() == 'DisabilityType.${json['primaryDisability']}',
      ),
      secondaryDisabilities: (json['secondaryDisabilities'] as List<dynamic>?)
              ?.map((e) => DisabilityType.values.firstWhere(
                    (type) => type.toString() == 'DisabilityType.$e',
                  ))
              .toList() ??
          [],
      medicalHistory: (json['medicalHistory'] as List<dynamic>?)
              ?.map((e) => MedicalHistory.fromJson(e))
              .toList() ??
          [],
      allergies: List<String>.from(json['allergies'] ?? []),
      currentMedications: List<String>.from(json['currentMedications'] ?? []),
      emergencyContact: EmergencyContact.fromJson(json['emergencyContact']),
      insuranceProvider: json['insuranceProvider'],
      insuranceNumber: json['insuranceNumber'],
      additionalNotes: json['additionalNotes'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'dateOfBirth': dateOfBirth.toIso8601String(),
      'gender': gender.toString().split('.').last,
      'nationalId': nationalId,
      'address': address,
      'city': city,
      'country': country,
      'primaryDisability': primaryDisability.toString().split('.').last,
      'secondaryDisabilities': secondaryDisabilities
          .map((e) => e.toString().split('.').last)
          .toList(),
      'medicalHistory': medicalHistory.map((e) => e.toJson()).toList(),
      'allergies': allergies,
      'currentMedications': currentMedications,
      'emergencyContact': emergencyContact.toJson(),
      'insuranceProvider': insuranceProvider,
      'insuranceNumber': insuranceNumber,
      'additionalNotes': additionalNotes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month ||
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  Patient copyWith({
    String? userId,
    DateTime? dateOfBirth,
    Gender? gender,
    String? nationalId,
    String? address,
    String? city,
    String? country,
    DisabilityType? primaryDisability,
    List<DisabilityType>? secondaryDisabilities,
    List<MedicalHistory>? medicalHistory,
    List<String>? allergies,
    List<String>? currentMedications,
    EmergencyContact? emergencyContact,
    String? insuranceProvider,
    String? insuranceNumber,
    Map<String, dynamic>? additionalNotes,
    DateTime? updatedAt,
  }) {
    return Patient(
      id: id,
      userId: userId ?? this.userId,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      nationalId: nationalId ?? this.nationalId,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      primaryDisability: primaryDisability ?? this.primaryDisability,
      secondaryDisabilities: secondaryDisabilities ?? this.secondaryDisabilities,
      medicalHistory: medicalHistory ?? this.medicalHistory,
      allergies: allergies ?? this.allergies,
      currentMedications: currentMedications ?? this.currentMedications,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      insuranceProvider: insuranceProvider ?? this.insuranceProvider,
      insuranceNumber: insuranceNumber ?? this.insuranceNumber,
      additionalNotes: additionalNotes ?? this.additionalNotes,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Patient && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Patient(id: $id, userId: $userId, age: $age, primaryDisability: $primaryDisability)';
  }
}
