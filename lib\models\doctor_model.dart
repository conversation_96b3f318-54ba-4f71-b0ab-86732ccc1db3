import 'package:uuid/uuid.dart';

enum Specialization {
  physiotherapy,
  prosthetics,
  orthopedics,
  neurology,
  rehabilitation,
  occupationalTherapy,
  psychology,
  generalMedicine,
  other,
}

enum DoctorStatus {
  active,
  inactive,
  pending,
  suspended,
  verified,
}

class Certification {
  final String name;
  final String issuingOrganization;
  final DateTime issueDate;
  final DateTime? expiryDate;
  final String? certificateNumber;
  final String? documentUrl;

  Certification({
    required this.name,
    required this.issuingOrganization,
    required this.issueDate,
    this.expiryDate,
    this.certificateNumber,
    this.documentUrl,
  });

  factory Certification.fromJson(Map<String, dynamic> json) {
    return Certification(
      name: json['name'],
      issuingOrganization: json['issuingOrganization'],
      issueDate: DateTime.parse(json['issueDate']),
      expiryDate: json['expiryDate'] != null
          ? DateTime.parse(json['expiryDate'])
          : null,
      certificateNumber: json['certificateNumber'],
      documentUrl: json['documentUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'issuingOrganization': issuingOrganization,
      'issueDate': issueDate.toIso8601String(),
      'expiryDate': expiryDate?.toIso8601String(),
      'certificateNumber': certificateNumber,
      'documentUrl': documentUrl,
    };
  }

  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }
}

class WorkExperience {
  final String position;
  final String organization;
  final DateTime startDate;
  final DateTime? endDate;
  final String? description;
  final bool isCurrent;

  WorkExperience({
    required this.position,
    required this.organization,
    required this.startDate,
    this.endDate,
    this.description,
    this.isCurrent = false,
  });

  factory WorkExperience.fromJson(Map<String, dynamic> json) {
    return WorkExperience(
      position: json['position'],
      organization: json['organization'],
      startDate: DateTime.parse(json['startDate']),
      endDate: json['endDate'] != null
          ? DateTime.parse(json['endDate'])
          : null,
      description: json['description'],
      isCurrent: json['isCurrent'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'position': position,
      'organization': organization,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'description': description,
      'isCurrent': isCurrent,
    };
  }

  Duration get duration {
    final end = endDate ?? DateTime.now();
    return end.difference(startDate);
  }
}

class Doctor {
  final String id;
  final String userId; // Reference to User model
  final String licenseNumber;
  final Specialization primarySpecialization;
  final List<Specialization> secondarySpecializations;
  final List<Certification> certifications;
  final List<WorkExperience> workExperience;
  final String? bio;
  final List<String> languages;
  final String? clinicAddress;
  final String? clinicPhone;
  final Map<String, String>? workingHours; // day -> hours (e.g., "monday" -> "9:00-17:00")
  final double? consultationFee;
  final bool isAvailableForChat;
  final bool isAvailableForConsultation;
  final double rating;
  final int totalReviews;
  final DoctorStatus status;
  final DateTime? verifiedAt;
  final String? verifiedBy; // Admin user ID
  final DateTime createdAt;
  final DateTime updatedAt;

  Doctor({
    String? id,
    required this.userId,
    required this.licenseNumber,
    required this.primarySpecialization,
    this.secondarySpecializations = const [],
    this.certifications = const [],
    this.workExperience = const [],
    this.bio,
    this.languages = const [],
    this.clinicAddress,
    this.clinicPhone,
    this.workingHours,
    this.consultationFee,
    this.isAvailableForChat = true,
    this.isAvailableForConsultation = true,
    this.rating = 0.0,
    this.totalReviews = 0,
    this.status = DoctorStatus.pending,
    this.verifiedAt,
    this.verifiedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory Doctor.fromJson(Map<String, dynamic> json) {
    return Doctor(
      id: json['id'],
      userId: json['userId'],
      licenseNumber: json['licenseNumber'],
      primarySpecialization: Specialization.values.firstWhere(
        (e) => e.toString() == 'Specialization.${json['primarySpecialization']}',
      ),
      secondarySpecializations: (json['secondarySpecializations'] as List<dynamic>?)
              ?.map((e) => Specialization.values.firstWhere(
                    (spec) => spec.toString() == 'Specialization.$e',
                  ))
              .toList() ??
          [],
      certifications: (json['certifications'] as List<dynamic>?)
              ?.map((e) => Certification.fromJson(e))
              .toList() ??
          [],
      workExperience: (json['workExperience'] as List<dynamic>?)
              ?.map((e) => WorkExperience.fromJson(e))
              .toList() ??
          [],
      bio: json['bio'],
      languages: List<String>.from(json['languages'] ?? []),
      clinicAddress: json['clinicAddress'],
      clinicPhone: json['clinicPhone'],
      workingHours: json['workingHours'] != null
          ? Map<String, String>.from(json['workingHours'])
          : null,
      consultationFee: json['consultationFee']?.toDouble(),
      isAvailableForChat: json['isAvailableForChat'] ?? true,
      isAvailableForConsultation: json['isAvailableForConsultation'] ?? true,
      rating: json['rating']?.toDouble() ?? 0.0,
      totalReviews: json['totalReviews'] ?? 0,
      status: DoctorStatus.values.firstWhere(
        (e) => e.toString() == 'DoctorStatus.${json['status']}',
      ),
      verifiedAt: json['verifiedAt'] != null
          ? DateTime.parse(json['verifiedAt'])
          : null,
      verifiedBy: json['verifiedBy'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'licenseNumber': licenseNumber,
      'primarySpecialization': primarySpecialization.toString().split('.').last,
      'secondarySpecializations': secondarySpecializations
          .map((e) => e.toString().split('.').last)
          .toList(),
      'certifications': certifications.map((e) => e.toJson()).toList(),
      'workExperience': workExperience.map((e) => e.toJson()).toList(),
      'bio': bio,
      'languages': languages,
      'clinicAddress': clinicAddress,
      'clinicPhone': clinicPhone,
      'workingHours': workingHours,
      'consultationFee': consultationFee,
      'isAvailableForChat': isAvailableForChat,
      'isAvailableForConsultation': isAvailableForConsultation,
      'rating': rating,
      'totalReviews': totalReviews,
      'status': status.toString().split('.').last,
      'verifiedAt': verifiedAt?.toIso8601String(),
      'verifiedBy': verifiedBy,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  int get totalExperienceYears {
    if (workExperience.isEmpty) return 0;
    
    int totalDays = 0;
    for (final exp in workExperience) {
      totalDays += exp.duration.inDays;
    }
    
    return (totalDays / 365).floor();
  }

  bool get isVerified => status == DoctorStatus.verified;

  Doctor copyWith({
    String? userId,
    String? licenseNumber,
    Specialization? primarySpecialization,
    List<Specialization>? secondarySpecializations,
    List<Certification>? certifications,
    List<WorkExperience>? workExperience,
    String? bio,
    List<String>? languages,
    String? clinicAddress,
    String? clinicPhone,
    Map<String, String>? workingHours,
    double? consultationFee,
    bool? isAvailableForChat,
    bool? isAvailableForConsultation,
    double? rating,
    int? totalReviews,
    DoctorStatus? status,
    DateTime? verifiedAt,
    String? verifiedBy,
    DateTime? updatedAt,
  }) {
    return Doctor(
      id: id,
      userId: userId ?? this.userId,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      primarySpecialization: primarySpecialization ?? this.primarySpecialization,
      secondarySpecializations: secondarySpecializations ?? this.secondarySpecializations,
      certifications: certifications ?? this.certifications,
      workExperience: workExperience ?? this.workExperience,
      bio: bio ?? this.bio,
      languages: languages ?? this.languages,
      clinicAddress: clinicAddress ?? this.clinicAddress,
      clinicPhone: clinicPhone ?? this.clinicPhone,
      workingHours: workingHours ?? this.workingHours,
      consultationFee: consultationFee ?? this.consultationFee,
      isAvailableForChat: isAvailableForChat ?? this.isAvailableForChat,
      isAvailableForConsultation: isAvailableForConsultation ?? this.isAvailableForConsultation,
      rating: rating ?? this.rating,
      totalReviews: totalReviews ?? this.totalReviews,
      status: status ?? this.status,
      verifiedAt: verifiedAt ?? this.verifiedAt,
      verifiedBy: verifiedBy ?? this.verifiedBy,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Doctor && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Doctor(id: $id, userId: $userId, specialization: $primarySpecialization, status: $status)';
  }
}
