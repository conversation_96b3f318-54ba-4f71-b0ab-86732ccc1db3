import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';
import '../../models/user_model.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/common/gradient_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/loading_widget.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String? _userRole; // Track which role was selected from welcome screen

  // Admin credentials
  static const String adminEmail = '<EMAIL>';
  static const String adminPassword = 'admin123';

  @override
  void initState() {
    super.initState();
    // Get the role argument passed from welcome screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final args = ModalRoute.of(context)?.settings.arguments as String?;
      setState(() {
        _userRole = args;
      });

      // Pre-fill admin credentials if admin role is selected
      if (_userRole == 'admin') {
        _emailController.text = adminEmail;
        _passwordController.text = adminPassword;
      }
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF6B73FF), Color(0xFF9B59B6)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                const SizedBox(height: 40),

                // Logo
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.medical_services,
                    size: 50,
                    color: Color(0xFF6B73FF),
                  ),
                ),

                const SizedBox(height: 32),

                // Title
                Text(
                  _getRoleTitle(),
                  style: AppTheme.heading3.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),

                const SizedBox(height: 8),

                Text(
                  'أدخل بياناتك للوصول إلى حسابك',
                  style: AppTheme.bodyMedium.copyWith(
                    color: AppColors.white.withOpacity(0.9),
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 40),

                // Login Form
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        EmailTextField(
                          controller: _emailController,
                          validator: (value) {
                            if (value?.isEmpty ?? true) {
                              return AppConstants.errorRequiredField;
                            }
                            if (!AppConstants.emailRegex.hasMatch(value!)) {
                              return AppConstants.errorInvalidEmail;
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        PasswordTextField(
                          controller: _passwordController,
                          validator: (value) {
                            if (value?.isEmpty ?? true) {
                              return AppConstants.errorRequiredField;
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 8),

                        // Forgot Password
                        Align(
                          alignment: Alignment.centerLeft,
                          child: TextButton(
                            onPressed: () =>
                                _showComingSoon('نسيت كلمة المرور'),
                            child: Text(
                              'نسيت كلمة المرور؟',
                              style: AppTheme.labelMedium.copyWith(
                                color: const Color(0xFF6B73FF),
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Login Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _login,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF6B73FF),
                              foregroundColor: AppColors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: _isLoading
                                ? const SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        AppColors.white,
                                      ),
                                    ),
                                  )
                                : Text(
                                    'تسجيل الدخول',
                                    style: AppTheme.labelLarge.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 32),

                // Register Link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'ليس لديك حساب؟ ',
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppColors.white.withOpacity(0.8),
                      ),
                    ),
                    GestureDetector(
                      onTap: () => _navigateToRegistration(),
                      child: Text(
                        'إنشاء حساب جديد',
                        style: AppTheme.labelLarge.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Demo Mode Button (Development Feature)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.white.withOpacity(0.3)),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(Icons.science, color: AppColors.white, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'وضع التجربة (للمطورين)',
                            style: AppTheme.labelMedium.copyWith(
                              color: AppColors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: _buildDemoButton(
                              'مريض تجريبي',
                              Icons.person,
                              () => _loginAsDemo('patient'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _buildDemoButton(
                              'طبيب تجريبي',
                              Icons.medical_services,
                              () => _loginAsDemo('doctor'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final email = _emailController.text.trim();
      final password = _passwordController.text;

      // Handle admin login with hardcoded credentials
      if (_userRole == 'admin') {
        if (email == adminEmail && password == adminPassword) {
          // Simulate admin login success
          await Future.delayed(const Duration(seconds: 1));
          if (mounted) {
            Navigator.pushReplacementNamed(context, '/admin/dashboard');
          }
          return;
        } else {
          _showError('بيانات الإدارة غير صحيحة');
          return;
        }
      }

      // Handle regular user login
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final success = await authProvider.login(
        email: email,
        password: password,
      );

      if (success && mounted) {
        // Navigate based on user type and role preference
        final userType = authProvider.userType;

        // Validate that the logged-in user type matches the selected role
        if (_userRole == 'patient' && userType != UserType.patient) {
          _showError('هذا الحساب ليس حساب مريض');
          await authProvider.logout();
          return;
        }

        if (_userRole == 'doctor' && userType != UserType.doctor) {
          _showError('هذا الحساب ليس حساب طبيب');
          await authProvider.logout();
          return;
        }

        // Navigate to appropriate dashboard
        switch (userType) {
          case UserType.patient:
            Navigator.pushReplacementNamed(context, '/patient/dashboard');
            break;
          case UserType.doctor:
            Navigator.pushReplacementNamed(context, '/doctor/dashboard');
            break;
          case UserType.admin:
            Navigator.pushReplacementNamed(context, '/admin/dashboard');
            break;
          default:
            Navigator.pushReplacementNamed(context, '/home');
        }
      } else if (mounted) {
        _showError(authProvider.errorMessage ?? 'فشل في تسجيل الدخول');
      }
    } catch (e) {
      if (mounted) {
        _showError('حدث خطأ غير متوقع');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: AppColors.error),
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('ميزة $feature ستكون متاحة قريباً'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  String _getRoleTitle() {
    switch (_userRole) {
      case 'patient':
        return 'تسجيل دخول المرضى';
      case 'doctor':
        return 'تسجيل دخول الأطباء';
      case 'admin':
        return 'تسجيل دخول الإدارة';
      default:
        return 'تسجيل الدخول';
    }
  }

  void _navigateToRegistration() {
    if (_userRole == 'admin') {
      _showError('لا يمكن إنشاء حساب إدارة جديد');
      return;
    }

    // Navigate to registration with role preference
    Navigator.pushNamed(
      context,
      '/registration-selection',
      arguments: _userRole,
    );
  }

  Widget _buildDemoButton(String text, IconData icon, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: AppColors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.white.withOpacity(0.4)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: AppColors.white, size: 16),
            const SizedBox(width: 6),
            Flexible(
              child: Text(
                text,
                style: AppTheme.labelSmall.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.bold,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _loginAsDemo(String demoType) async {
    setState(() => _isLoading = true);

    try {
      // Simulate loading delay
      await Future.delayed(const Duration(seconds: 1));

      if (mounted) {
        // Navigate directly to the appropriate dashboard with demo data
        switch (demoType) {
          case 'patient':
            Navigator.pushReplacementNamed(context, '/patient/dashboard');
            break;
          case 'doctor':
            Navigator.pushReplacementNamed(context, '/doctor/dashboard');
            break;
        }

        // Show demo mode notification
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تسجيل الدخول في وضع التجربة - ${demoType == 'patient' ? 'مريض' : 'طبيب'} تجريبي',
            ),
            backgroundColor: AppColors.success,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        _showError('فشل في تشغيل وضع التجربة');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
