import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';
import '../../widgets/common/gradient_button.dart';

class ChooseAccountTypeScreen extends StatelessWidget {
  const ChooseAccountTypeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF6B73FF),
              Color(0xFF9B59B6),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                const SizedBox(height: 40),
                
                // Logo
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.medical_services,
                    size: 60,
                    color: Color(0xFF6B73FF),
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Title
                Text(
                  'اختر نوع حسابك',
                  style: AppTheme.heading3.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 16),
                
                // Subtitle
                Text(
                  'اختر نوع الحساب المناسب لك للمتابعة',
                  style: AppTheme.bodyLarge.copyWith(
                    color: AppColors.white.withOpacity(0.9),
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 60),
                
                // Account Type Cards
                Expanded(
                  child: Column(
                    children: [
                      _buildAccountTypeCard(
                        context: context,
                        title: 'مريض',
                        subtitle: 'أحتاج إلى رعاية طبية واستشارات',
                        icon: Icons.person,
                        gradient: AppColors.patientGradient,
                        onTap: () => _navigateToRegistration(context, 'patient'),
                      ),
                      
                      const SizedBox(height: 20),
                      
                      _buildAccountTypeCard(
                        context: context,
                        title: 'طبيب / مختص',
                        subtitle: 'أقدم الخدمات الطبية والاستشارات',
                        icon: Icons.medical_services,
                        gradient: AppColors.doctorGradient,
                        onTap: () => _navigateToRegistration(context, 'doctor'),
                      ),
                      
                      const SizedBox(height: 40),
                      
                      // Login Link
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'لديك حساب بالفعل؟ ',
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppColors.white.withOpacity(0.8),
                            ),
                          ),
                          GestureDetector(
                            onTap: () => _navigateToLogin(context),
                            child: Text(
                              'تسجيل الدخول',
                              style: AppTheme.labelLarge.copyWith(
                                color: AppColors.white,
                                fontWeight: FontWeight.bold,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAccountTypeCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Gradient gradient,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppColors.white.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 30,
                color: AppColors.white,
              ),
            ),
            
            const SizedBox(height: 16),
            
            Text(
              title,
              style: AppTheme.heading5.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              subtitle,
              style: AppTheme.bodyMedium.copyWith(
                color: AppColors.white.withOpacity(0.9),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToRegistration(BuildContext context, String userType) {
    if (userType == 'patient') {
      Navigator.pushNamed(context, '/patient/registration');
    } else if (userType == 'doctor') {
      Navigator.pushNamed(context, '/doctor/registration');
    }
  }

  void _navigateToLogin(BuildContext context) {
    Navigator.pushNamed(context, '/login');
  }
}
