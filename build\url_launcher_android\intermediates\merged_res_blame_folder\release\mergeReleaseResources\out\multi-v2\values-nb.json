{"logs": [{"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-release-26:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "781,891,992,1104", "endColumns": "109,100,111,96", "endOffsets": "886,987,1099,1196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,1201", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,1297"}}]}, {"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-mergeReleaseResources-24:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "781,891,992,1104", "endColumns": "109,100,111,96", "endOffsets": "886,987,1099,1196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,1201", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,1297"}}]}]}