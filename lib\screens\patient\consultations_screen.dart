import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';
import '../../models/doctor_model.dart';
import '../../models/consultation_model.dart';
import '../../widgets/common/gradient_button.dart';

class ConsultationsScreen extends StatefulWidget {
  const ConsultationsScreen({super.key});

  @override
  State<ConsultationsScreen> createState() => _ConsultationsScreenState();
}

class _ConsultationsScreenState extends State<ConsultationsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<Doctor> _availableDoctors = [];
  List<Consultation> _myConsultations = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    // Simulate loading data
    await Future.delayed(const Duration(seconds: 1));

    // Mock data for available doctors
    _availableDoctors = [
      Doctor(
        id: 'doc1',
        userId: 'user1',
        licenseNumber: 'LIC001',
        primarySpecialization: Specialization.generalMedicine,
        bio: 'طبيب عام متخصص في الطب الباطني مع خبرة 10 سنوات',
        languages: ['العربية', 'الإنجليزية'],
        status: DoctorStatus.verified,
        consultationFee: 200.0,
        rating: 4.8,
        totalReviews: 150,
      ),
      Doctor(
        id: 'doc2',
        userId: 'user2',
        licenseNumber: 'LIC002',
        primarySpecialization: Specialization.neurology,
        bio: 'استشاري أمراض القلب والأوعية الدموية',
        languages: ['العربية', 'الإنجليزية', 'الفرنسية'],
        status: DoctorStatus.verified,
        consultationFee: 350.0,
        rating: 4.9,
        totalReviews: 200,
      ),
      Doctor(
        id: 'doc3',
        userId: 'user3',
        licenseNumber: 'LIC003',
        primarySpecialization: Specialization.orthopedics,
        bio: 'استشاري الأمراض العصبية والدماغ',
        languages: ['العربية', 'الإنجليزية'],
        status: DoctorStatus.verified,
        consultationFee: 300.0,
        rating: 4.7,
        totalReviews: 120,
      ),
    ];

    // Mock data for my consultations
    _myConsultations = [
      Consultation(
        id: 'cons1',
        patientId: 'patient1',
        doctorId: 'doc1',
        type: ConsultationType.video,
        status: ConsultationStatus.scheduled,
        title: 'استشارة عامة',
        description: 'فحص دوري عام',
        scheduledAt: DateTime.now().add(const Duration(days: 2)),
        fee: 200.0,
      ),
      Consultation(
        id: 'cons2',
        patientId: 'patient1',
        doctorId: 'doc2',
        type: ConsultationType.chat,
        status: ConsultationStatus.completed,
        title: 'استشارة أمراض القلب',
        description: 'متابعة ضغط الدم',
        scheduledAt: DateTime.now().subtract(const Duration(days: 5)),
        completedAt: DateTime.now().subtract(const Duration(days: 5)),
        fee: 350.0,
        prescriptions: ['دواء للضغط - مرة واحدة يومياً'],
      ),
    ];

    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الاستشارات الطبية'),
        backgroundColor: AppColors.patientPrimary,
        foregroundColor: AppColors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.white,
          labelColor: AppColors.white,
          unselectedLabelColor: AppColors.white.withOpacity(0.7),
          tabs: const [
            Tab(text: 'الأطباء المتاحون'),
            Tab(text: 'استشاراتي'),
            Tab(text: 'السجل الطبي'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildAvailableDoctors(),
                _buildMyConsultations(),
                _buildMedicalHistory(),
              ],
            ),
    );
  }

  Widget _buildAvailableDoctors() {
    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _availableDoctors.length,
        itemBuilder: (context, index) {
          final doctor = _availableDoctors[index];
          return _buildDoctorCard(doctor);
        },
      ),
    );
  }

  Widget _buildDoctorCard(Doctor doctor) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: () => _showDoctorDetails(doctor),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Doctor Avatar
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: AppColors.patientPrimary.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.person,
                      size: 30,
                      color: AppColors.patientPrimary,
                    ),
                  ),
                  const SizedBox(width: 16),

                  // Doctor Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'د. ${_getDoctorName(doctor.id)}',
                          style: AppTheme.heading6.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _getSpecializationName(doctor.primarySpecialization),
                          style: AppTheme.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.star,
                              size: 16,
                              color: AppColors.warning,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${doctor.rating} (${doctor.totalReviews} تقييم)',
                              style: AppTheme.bodySmall,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Consultation Fee
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.success.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${doctor.consultationFee?.toInt() ?? 0} ر.س',
                      style: AppTheme.bodySmall.copyWith(
                        color: AppColors.success,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Doctor Bio
              Text(
                doctor.bio ?? 'لا توجد معلومات إضافية',
                style: AppTheme.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 12),

              // Experience and Languages
              Row(
                children: [
                  _buildInfoChip(icon: Icons.work_outline, label: 'خبرة طبية'),
                  const SizedBox(width: 8),
                  _buildInfoChip(
                    icon: Icons.language,
                    label: doctor.languages.join(', '),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () =>
                          _bookConsultation(doctor, ConsultationType.chat),
                      icon: const Icon(Icons.chat_outlined),
                      label: const Text('محادثة'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.patientPrimary,
                        side: BorderSide(color: AppColors.patientPrimary),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () =>
                          _bookConsultation(doctor, ConsultationType.video),
                      icon: const Icon(Icons.video_call_outlined),
                      label: const Text('مكالمة فيديو'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.patientPrimary,
                        foregroundColor: AppColors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip({required IconData icon, required String label}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: AppColors.textSecondary),
          const SizedBox(width: 4),
          Text(
            label,
            style: AppTheme.caption.copyWith(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildMyConsultations() {
    if (_myConsultations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.medical_services_outlined,
              size: 64,
              color: AppColors.textSecondary.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد استشارات حالياً',
              style: AppTheme.heading6.copyWith(color: AppColors.textSecondary),
            ),
            const SizedBox(height: 8),
            Text(
              'احجز استشارتك الأولى مع أحد أطبائنا المتخصصين',
              style: AppTheme.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            PatientButton(
              text: 'احجز استشارة',
              onPressed: () => _tabController.animateTo(0),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _myConsultations.length,
      itemBuilder: (context, index) {
        final consultation = _myConsultations[index];
        return _buildConsultationCard(consultation);
      },
    );
  }

  Widget _buildConsultationCard(Consultation consultation) {
    final doctor = _availableDoctors.firstWhere(
      (d) => d.id == consultation.doctorId,
      orElse: () => _availableDoctors.first,
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Status Badge
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      consultation.status,
                    ).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _getStatusText(consultation.status),
                    style: AppTheme.caption.copyWith(
                      color: _getStatusColor(consultation.status),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Spacer(),
                // Consultation Type
                Icon(
                  consultation.type == ConsultationType.video
                      ? Icons.video_call
                      : Icons.chat,
                  color: AppColors.patientPrimary,
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Doctor Info
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.patientPrimary.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.person, color: AppColors.patientPrimary),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'د. ${_getDoctorName(doctor.id)}',
                        style: AppTheme.labelLarge.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _getSpecializationName(doctor.primarySpecialization),
                        style: AppTheme.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Consultation Details
            _buildDetailRow(
              icon: Icons.calendar_today,
              label: 'التاريخ',
              value: consultation.scheduledAt != null
                  ? _formatDate(consultation.scheduledAt!)
                  : 'غير محدد',
            ),
            const SizedBox(height: 8),
            _buildDetailRow(
              icon: Icons.access_time,
              label: 'الوقت',
              value: consultation.scheduledAt != null
                  ? _formatTime(consultation.scheduledAt!)
                  : 'غير محدد',
            ),
            const SizedBox(height: 8),
            _buildDetailRow(
              icon: Icons.attach_money,
              label: 'الرسوم',
              value: '${consultation.fee?.toInt() ?? 0} ر.س',
            ),

            if (consultation.description.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'ملاحظات:',
                style: AppTheme.labelMedium.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                consultation.description,
                style: AppTheme.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],

            if (consultation.prescriptions.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.success.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.success.withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.medication,
                          size: 16,
                          color: AppColors.success,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'الوصفة الطبية:',
                          style: AppTheme.labelMedium.copyWith(
                            color: AppColors.success,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      consultation.prescriptions.join('\n'),
                      style: AppTheme.bodySmall.copyWith(
                        color: AppColors.success,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 16),

            // Action Buttons
            if (consultation.status == ConsultationStatus.scheduled) ...[
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => _cancelConsultation(consultation),
                      child: const Text('إلغاء'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.error,
                        side: BorderSide(color: AppColors.error),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _joinConsultation(consultation),
                      child: const Text('انضمام'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.patientPrimary,
                        foregroundColor: AppColors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ] else if (consultation.status == ConsultationStatus.completed) ...[
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () => _rateConsultation(consultation),
                  icon: const Icon(Icons.star_outline),
                  label: const Text('تقييم الاستشارة'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.warning,
                    side: BorderSide(color: AppColors.warning),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(icon, size: 16, color: AppColors.textSecondary),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: AppTheme.bodySmall.copyWith(color: AppColors.textSecondary),
        ),
        Text(
          value,
          style: AppTheme.bodySmall.copyWith(fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  Widget _buildMedicalHistory() {
    return const Center(
      child: Text(
        'سجل الاستشارات الطبية\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16),
      ),
    );
  }

  // Helper Methods
  String _getDoctorName(String doctorId) {
    switch (doctorId) {
      case 'doc1':
        return 'أحمد محمد';
      case 'doc2':
        return 'فاطمة علي';
      case 'doc3':
        return 'محمد حسن';
      default:
        return 'طبيب مجهول';
    }
  }

  String _getSpecializationName(Specialization specialization) {
    switch (specialization) {
      case Specialization.physiotherapy:
        return 'العلاج الطبيعي';
      case Specialization.prosthetics:
        return 'الأطراف الصناعية';
      case Specialization.orthopedics:
        return 'العظام';
      case Specialization.neurology:
        return 'أمراض الأعصاب';
      case Specialization.rehabilitation:
        return 'التأهيل الطبي';
      case Specialization.occupationalTherapy:
        return 'العلاج الوظيفي';
      case Specialization.psychology:
        return 'علم النفس';
      case Specialization.generalMedicine:
        return 'طب عام';
      case Specialization.other:
        return 'تخصص آخر';
    }
  }

  Color _getStatusColor(ConsultationStatus status) {
    switch (status) {
      case ConsultationStatus.pending:
        return AppColors.warning;
      case ConsultationStatus.accepted:
        return AppColors.info;
      case ConsultationStatus.scheduled:
        return AppColors.info;
      case ConsultationStatus.inProgress:
        return AppColors.warning;
      case ConsultationStatus.completed:
        return AppColors.success;
      case ConsultationStatus.cancelled:
        return AppColors.error;
      case ConsultationStatus.rejected:
        return AppColors.error;
    }
  }

  String _getStatusText(ConsultationStatus status) {
    switch (status) {
      case ConsultationStatus.pending:
        return 'في الانتظار';
      case ConsultationStatus.accepted:
        return 'مقبولة';
      case ConsultationStatus.scheduled:
        return 'مجدولة';
      case ConsultationStatus.inProgress:
        return 'جارية';
      case ConsultationStatus.completed:
        return 'مكتملة';
      case ConsultationStatus.cancelled:
        return 'ملغية';
      case ConsultationStatus.rejected:
        return 'مرفوضة';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTime(DateTime date) {
    return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  // Action Methods
  void _showDoctorDetails(Doctor doctor) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DoctorDetailsBottomSheet(doctor: doctor),
    );
  }

  void _bookConsultation(Doctor doctor, ConsultationType type) {
    HapticFeedback.lightImpact();
    showDialog(
      context: context,
      builder: (context) => BookConsultationDialog(
        doctor: doctor,
        type: type,
        onBooked: (consultation) {
          setState(() {
            _myConsultations.add(consultation);
          });
          _tabController.animateTo(1);
        },
      ),
    );
  }

  void _cancelConsultation(Consultation consultation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء الاستشارة'),
        content: const Text('هل أنت متأكد من إلغاء هذه الاستشارة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('لا'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                final index = _myConsultations.indexOf(consultation);
                if (index != -1) {
                  _myConsultations[index] = Consultation(
                    id: consultation.id,
                    patientId: consultation.patientId,
                    doctorId: consultation.doctorId,
                    type: consultation.type,
                    status: ConsultationStatus.cancelled,
                    title: consultation.title,
                    description: consultation.description,
                    scheduledAt: consultation.scheduledAt,
                    fee: consultation.fee,
                  );
                }
              });
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('تم إلغاء الاستشارة')),
              );
            },
            child: const Text('نعم'),
          ),
        ],
      ),
    );
  }

  void _joinConsultation(Consultation consultation) {
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم توصيلك بالطبيب قريباً...'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _rateConsultation(Consultation consultation) {
    showDialog(
      context: context,
      builder: (context) => RateConsultationDialog(consultation: consultation),
    );
  }
}

// Additional Widgets will be added in separate files
class DoctorDetailsBottomSheet extends StatelessWidget {
  final Doctor doctor;

  const DoctorDetailsBottomSheet({super.key, required this.doctor});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: const Center(child: Text('تفاصيل الطبيب\n(قيد التطوير)')),
    );
  }
}

class BookConsultationDialog extends StatelessWidget {
  final Doctor doctor;
  final ConsultationType type;
  final Function(Consultation) onBooked;

  const BookConsultationDialog({
    super.key,
    required this.doctor,
    required this.type,
    required this.onBooked,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('حجز استشارة'),
      content: Text(
        'سيتم حجز استشارة ${type == ConsultationType.video ? 'فيديو' : 'محادثة'} مع د. ${_getDoctorName(doctor.id)}',
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            final consultation = Consultation(
              id: 'cons_${DateTime.now().millisecondsSinceEpoch}',
              patientId: 'patient1',
              doctorId: doctor.id,
              type: type,
              status: ConsultationStatus.scheduled,
              title:
                  'استشارة ${type == ConsultationType.video ? 'فيديو' : 'محادثة'}',
              description: 'استشارة جديدة مع د. ${_getDoctorName(doctor.id)}',
              scheduledAt: DateTime.now().add(const Duration(days: 1)),
              fee: doctor.consultationFee,
            );
            onBooked(consultation);
            Navigator.pop(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم حجز الاستشارة بنجاح'),
                backgroundColor: AppColors.success,
              ),
            );
          },
          child: const Text('تأكيد الحجز'),
        ),
      ],
    );
  }

  String _getDoctorName(String doctorId) {
    switch (doctorId) {
      case 'doc1':
        return 'أحمد محمد';
      case 'doc2':
        return 'فاطمة علي';
      case 'doc3':
        return 'محمد حسن';
      default:
        return 'طبيب مجهول';
    }
  }
}

class RateConsultationDialog extends StatelessWidget {
  final Consultation consultation;

  const RateConsultationDialog({super.key, required this.consultation});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تقييم الاستشارة'),
      content: const Text('قيم تجربتك مع الطبيب'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إغلاق'),
        ),
      ],
    );
  }
}
