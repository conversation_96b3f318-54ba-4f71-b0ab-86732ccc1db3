import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'constants/app_constants.dart';
import 'constants/app_theme.dart';
import 'providers/auth_provider.dart';
import 'screens/splash_screen.dart';
import 'screens/auth/welcome_screen.dart';
import 'screens/auth/choose_account_type_screen.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/patient_registration_screen.dart';
import 'screens/auth/doctor_registration_screen.dart';
import 'screens/home/<USER>';
import 'screens/patient/patient_dashboard.dart';
import 'screens/patient/medical_file_screen.dart';
import 'screens/doctor/doctor_dashboard.dart';
import 'screens/admin/admin_dashboard.dart';
import 'screens/admin/doctor_verification_screen.dart';
import 'screens/chat/chat_list_screen.dart';
import 'screens/maps/medical_centers_map_screen.dart';
import 'screens/education/educational_content_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  runApp(const KhatwaApp());
}

class KhatwaApp extends StatelessWidget {
  const KhatwaApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [ChangeNotifierProvider(create: (_) => AuthProvider())],
      child: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          return MaterialApp(
            title: AppConstants.appName,
            debugShowCheckedModeBanner: false,

            // Localization
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('ar', 'SA'), // Arabic (Saudi Arabia)
              Locale('en', 'US'), // English (fallback)
            ],
            locale: const Locale('ar', 'SA'),

            // Theme
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.light,

            // Routes
            initialRoute: '/',
            routes: {
              '/': (context) => const SplashScreen(),
              '/welcome': (context) => const WelcomeScreen(),
              '/choose-account': (context) => const ChooseAccountTypeScreen(),
              '/login': (context) => const LoginScreen(),
              '/home': (context) => const HomeScreen(),
              '/patient/registration': (context) =>
                  const PatientRegistrationScreen(),
              '/doctor/registration': (context) =>
                  const DoctorRegistrationScreen(),
              '/patient/dashboard': (context) => const PatientDashboard(),
              '/patient/medical-file': (context) => const MedicalFileScreen(),
              '/doctor/dashboard': (context) => const DoctorDashboard(),
              '/admin/dashboard': (context) => const AdminDashboard(),
              '/admin/doctor-verification': (context) =>
                  const DoctorVerificationScreen(),
              '/chat': (context) => const ChatListScreen(),
              '/maps': (context) => const MedicalCentersMapScreen(),
              '/education': (context) => const EducationalContentScreen(),
            },
          );
        },
      ),
    );
  }
}
