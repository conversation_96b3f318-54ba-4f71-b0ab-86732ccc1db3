[{"merged": "com.tekartik.sqflite.sqflite_android-release-25:/layout-v21/notification_action.xml", "source": "com.tekartik.sqflite.sqflite_android-core-1.13.1-10:/layout-v21/notification_action.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/layout-v21/notification_action_tombstone.xml", "source": "com.tekartik.sqflite.sqflite_android-core-1.13.1-10:/layout-v21/notification_action_tombstone.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/layout-v21/notification_template_icon_group.xml", "source": "com.tekartik.sqflite.sqflite_android-core-1.13.1-10:/layout-v21/notification_template_icon_group.xml"}, {"merged": "com.tekartik.sqflite.sqflite_android-release-25:/layout-v21/notification_template_custom_big.xml", "source": "com.tekartik.sqflite.sqflite_android-core-1.13.1-10:/layout-v21/notification_template_custom_big.xml"}]