import 'package:uuid/uuid.dart';

enum CenterType {
  hospital,
  clinic,
  rehabilitationCenter,
  prostheticsCenter,
  physiotherapyCenter,
  specializedCenter,
}

enum ServiceType {
  physiotherapy,
  prosthetics,
  orthopedics,
  neurology,
  rehabilitation,
  occupationalTherapy,
  psychology,
  generalMedicine,
  surgery,
  diagnostics,
  other,
}

class WorkingHours {
  final String day;
  final String openTime;
  final String closeTime;
  final bool isClosed;

  WorkingHours({
    required this.day,
    required this.openTime,
    required this.closeTime,
    this.isClosed = false,
  });

  factory WorkingHours.fromJson(Map<String, dynamic> json) {
    return WorkingHours(
      day: json['day'],
      openTime: json['openTime'],
      closeTime: json['closeTime'],
      isClosed: json['isClosed'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day': day,
      'openTime': openTime,
      'closeTime': closeTime,
      'isClosed': isClosed,
    };
  }
}

class ContactInfo {
  final String? phone;
  final String? email;
  final String? website;
  final String? fax;

  ContactInfo({
    this.phone,
    this.email,
    this.website,
    this.fax,
  });

  factory ContactInfo.fromJson(Map<String, dynamic> json) {
    return ContactInfo(
      phone: json['phone'],
      email: json['email'],
      website: json['website'],
      fax: json['fax'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'phone': phone,
      'email': email,
      'website': website,
      'fax': fax,
    };
  }
}

class Location {
  final double latitude;
  final double longitude;
  final String address;
  final String city;
  final String state;
  final String country;
  final String? postalCode;

  Location({
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.city,
    required this.state,
    required this.country,
    this.postalCode,
  });

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      address: json['address'],
      city: json['city'],
      state: json['state'],
      country: json['country'],
      postalCode: json['postalCode'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'city': city,
      'state': state,
      'country': country,
      'postalCode': postalCode,
    };
  }

  String get fullAddress {
    final parts = [address, city, state, country];
    if (postalCode != null) parts.add(postalCode!);
    return parts.join(', ');
  }
}

class Review {
  final String id;
  final String userId;
  final String userName;
  final double rating;
  final String? comment;
  final DateTime createdAt;

  Review({
    String? id,
    required this.userId,
    required this.userName,
    required this.rating,
    this.comment,
    DateTime? createdAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now();

  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json['id'],
      userId: json['userId'],
      userName: json['userName'],
      rating: json['rating'].toDouble(),
      comment: json['comment'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'rating': rating,
      'comment': comment,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class MedicalCenter {
  final String id;
  final String name;
  final String? description;
  final CenterType type;
  final List<ServiceType> services;
  final Location location;
  final ContactInfo contactInfo;
  final List<WorkingHours> workingHours;
  final List<String> imageUrls;
  final List<String> certifications;
  final List<String> insuranceAccepted;
  final double rating;
  final int totalReviews;
  final List<Review> reviews;
  final bool isActive;
  final bool isVerified;
  final String? licenseNumber;
  final DateTime? verifiedAt;
  final Map<String, dynamic>? additionalInfo;
  final DateTime createdAt;
  final DateTime updatedAt;

  MedicalCenter({
    String? id,
    required this.name,
    this.description,
    required this.type,
    required this.services,
    required this.location,
    required this.contactInfo,
    this.workingHours = const [],
    this.imageUrls = const [],
    this.certifications = const [],
    this.insuranceAccepted = const [],
    this.rating = 0.0,
    this.totalReviews = 0,
    this.reviews = const [],
    this.isActive = true,
    this.isVerified = false,
    this.licenseNumber,
    this.verifiedAt,
    this.additionalInfo,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory MedicalCenter.fromJson(Map<String, dynamic> json) {
    return MedicalCenter(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      type: CenterType.values.firstWhere(
        (e) => e.toString() == 'CenterType.${json['type']}',
      ),
      services: (json['services'] as List<dynamic>)
          .map((e) => ServiceType.values.firstWhere(
                (service) => service.toString() == 'ServiceType.$e',
              ))
          .toList(),
      location: Location.fromJson(json['location']),
      contactInfo: ContactInfo.fromJson(json['contactInfo']),
      workingHours: (json['workingHours'] as List<dynamic>?)
              ?.map((e) => WorkingHours.fromJson(e))
              .toList() ??
          [],
      imageUrls: List<String>.from(json['imageUrls'] ?? []),
      certifications: List<String>.from(json['certifications'] ?? []),
      insuranceAccepted: List<String>.from(json['insuranceAccepted'] ?? []),
      rating: json['rating']?.toDouble() ?? 0.0,
      totalReviews: json['totalReviews'] ?? 0,
      reviews: (json['reviews'] as List<dynamic>?)
              ?.map((e) => Review.fromJson(e))
              .toList() ??
          [],
      isActive: json['isActive'] ?? true,
      isVerified: json['isVerified'] ?? false,
      licenseNumber: json['licenseNumber'],
      verifiedAt: json['verifiedAt'] != null
          ? DateTime.parse(json['verifiedAt'])
          : null,
      additionalInfo: json['additionalInfo'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.toString().split('.').last,
      'services': services.map((e) => e.toString().split('.').last).toList(),
      'location': location.toJson(),
      'contactInfo': contactInfo.toJson(),
      'workingHours': workingHours.map((e) => e.toJson()).toList(),
      'imageUrls': imageUrls,
      'certifications': certifications,
      'insuranceAccepted': insuranceAccepted,
      'rating': rating,
      'totalReviews': totalReviews,
      'reviews': reviews.map((e) => e.toJson()).toList(),
      'isActive': isActive,
      'isVerified': isVerified,
      'licenseNumber': licenseNumber,
      'verifiedAt': verifiedAt?.toIso8601String(),
      'additionalInfo': additionalInfo,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  bool hasService(ServiceType service) {
    return services.contains(service);
  }

  bool isOpenNow() {
    final now = DateTime.now();
    final dayName = _getDayName(now.weekday);
    
    final todayHours = workingHours.firstWhere(
      (hours) => hours.day.toLowerCase() == dayName.toLowerCase(),
      orElse: () => WorkingHours(day: dayName, openTime: '', closeTime: '', isClosed: true),
    );

    if (todayHours.isClosed) return false;

    final currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    return currentTime.compareTo(todayHours.openTime) >= 0 && 
           currentTime.compareTo(todayHours.closeTime) <= 0;
  }

  String _getDayName(int weekday) {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    return days[weekday - 1];
  }

  MedicalCenter copyWith({
    String? name,
    String? description,
    CenterType? type,
    List<ServiceType>? services,
    Location? location,
    ContactInfo? contactInfo,
    List<WorkingHours>? workingHours,
    List<String>? imageUrls,
    List<String>? certifications,
    List<String>? insuranceAccepted,
    double? rating,
    int? totalReviews,
    List<Review>? reviews,
    bool? isActive,
    bool? isVerified,
    String? licenseNumber,
    DateTime? verifiedAt,
    Map<String, dynamic>? additionalInfo,
    DateTime? updatedAt,
  }) {
    return MedicalCenter(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      services: services ?? this.services,
      location: location ?? this.location,
      contactInfo: contactInfo ?? this.contactInfo,
      workingHours: workingHours ?? this.workingHours,
      imageUrls: imageUrls ?? this.imageUrls,
      certifications: certifications ?? this.certifications,
      insuranceAccepted: insuranceAccepted ?? this.insuranceAccepted,
      rating: rating ?? this.rating,
      totalReviews: totalReviews ?? this.totalReviews,
      reviews: reviews ?? this.reviews,
      isActive: isActive ?? this.isActive,
      isVerified: isVerified ?? this.isVerified,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      verifiedAt: verifiedAt ?? this.verifiedAt,
      additionalInfo: additionalInfo ?? this.additionalInfo,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MedicalCenter && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MedicalCenter(id: $id, name: $name, type: $type, rating: $rating)';
  }
}
