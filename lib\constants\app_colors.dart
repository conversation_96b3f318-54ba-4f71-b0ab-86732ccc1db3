import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors for User Types
  
  // Patient Section - Blue Gradient
  static const Color patientPrimary = Color(0xFF2196F3);
  static const Color patientSecondary = Color(0xFF1976D2);
  static const Color patientLight = Color(0xFFE3F2FD);
  static const Color patientDark = Color(0xFF0D47A1);
  
  static const LinearGradient patientGradient = LinearGradient(
    colors: [patientPrimary, patientSecondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Doctor Section - Green Gradient
  static const Color doctorPrimary = Color(0xFF4CAF50);
  static const Color doctorSecondary = Color(0xFF388E3C);
  static const Color doctorLight = Color(0xFFE8F5E8);
  static const Color doctorDark = Color(0xFF1B5E20);
  
  static const LinearGradient doctorGradient = LinearGradient(
    colors: [doctorPrimary, doctorSecondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Admin Section - Red Gradient
  static const Color adminPrimary = Color(0xFFF44336);
  static const Color adminSecondary = Color(0xFFD32F2F);
  static const Color adminLight = Color(0xFFFFEBEE);
  static const Color adminDark = Color(0xFFB71C1C);
  
  static const LinearGradient adminGradient = LinearGradient(
    colors: [adminPrimary, adminSecondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Button Colors - Orange
  static const Color buttonPrimary = Color(0xFFFF9800);
  static const Color buttonSecondary = Color(0xFFF57C00);
  static const Color buttonLight = Color(0xFFFFF3E0);
  static const Color buttonDark = Color(0xFFE65100);
  
  static const LinearGradient buttonGradient = LinearGradient(
    colors: [buttonPrimary, buttonSecondary],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color transparent = Colors.transparent;
  
  // Grey Scale
  static const Color grey50 = Color(0xFFFAFAFA);
  static const Color grey100 = Color(0xFFF5F5F5);
  static const Color grey200 = Color(0xFFEEEEEE);
  static const Color grey300 = Color(0xFFE0E0E0);
  static const Color grey400 = Color(0xFFBDBDBD);
  static const Color grey500 = Color(0xFF9E9E9E);
  static const Color grey600 = Color(0xFF757575);
  static const Color grey700 = Color(0xFF616161);
  static const Color grey800 = Color(0xFF424242);
  static const Color grey900 = Color(0xFF212121);

  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);

  // Background Colors
  static const Color backgroundLight = Color(0xFFFAFAFA);
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surfaceLight = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF1E1E1E);

  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textDisabled = Color(0xFFBDBDBD);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnSecondary = Color(0xFFFFFFFF);

  // Border Colors
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderMedium = Color(0xFFBDBDBD);
  static const Color borderDark = Color(0xFF757575);

  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);

  // Chat Colors
  static const Color chatBubbleUser = Color(0xFF2196F3);
  static const Color chatBubbleOther = Color(0xFFE0E0E0);
  static const Color chatBubbleSystem = Color(0xFFFFF3E0);
  static const Color chatOnline = Color(0xFF4CAF50);
  static const Color chatOffline = Color(0xFF9E9E9E);
  static const Color chatTyping = Color(0xFFFF9800);

  // Rating Colors
  static const Color ratingFilled = Color(0xFFFFD700);
  static const Color ratingEmpty = Color(0xFFE0E0E0);

  // Priority Colors
  static const Color priorityLow = Color(0xFF4CAF50);
  static const Color priorityMedium = Color(0xFFFF9800);
  static const Color priorityHigh = Color(0xFFF44336);
  static const Color priorityUrgent = Color(0xFF9C27B0);

  // Consultation Status Colors
  static const Color statusPending = Color(0xFFFF9800);
  static const Color statusAccepted = Color(0xFF2196F3);
  static const Color statusInProgress = Color(0xFF9C27B0);
  static const Color statusCompleted = Color(0xFF4CAF50);
  static const Color statusCancelled = Color(0xFF9E9E9E);
  static const Color statusRejected = Color(0xFFF44336);

  // Specialty Colors
  static const Color physiotherapy = Color(0xFF00BCD4);
  static const Color prosthetics = Color(0xFF9C27B0);
  static const Color orthopedics = Color(0xFF3F51B5);
  static const Color neurology = Color(0xFFE91E63);
  static const Color rehabilitation = Color(0xFF8BC34A);
  static const Color occupationalTherapy = Color(0xFFFF5722);
  static const Color psychology = Color(0xFF607D8B);
  static const Color generalMedicine = Color(0xFF795548);

  // Gradient Combinations
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFF2196F3), Color(0xFF1976D2)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient successGradient = LinearGradient(
    colors: [Color(0xFF4CAF50), Color(0xFF388E3C)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient warningGradient = LinearGradient(
    colors: [Color(0xFFFF9800), Color(0xFFF57C00)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient errorGradient = LinearGradient(
    colors: [Color(0xFFF44336), Color(0xFFD32F2F)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Helper Methods
  static Color getUserTypeColor(String userType) {
    switch (userType.toLowerCase()) {
      case 'patient':
        return patientPrimary;
      case 'doctor':
        return doctorPrimary;
      case 'admin':
        return adminPrimary;
      default:
        return grey500;
    }
  }

  static LinearGradient getUserTypeGradient(String userType) {
    switch (userType.toLowerCase()) {
      case 'patient':
        return patientGradient;
      case 'doctor':
        return doctorGradient;
      case 'admin':
        return adminGradient;
      default:
        return primaryGradient;
    }
  }

  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return statusPending;
      case 'accepted':
        return statusAccepted;
      case 'inprogress':
      case 'in_progress':
        return statusInProgress;
      case 'completed':
        return statusCompleted;
      case 'cancelled':
        return statusCancelled;
      case 'rejected':
        return statusRejected;
      default:
        return grey500;
    }
  }

  static Color getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'low':
        return priorityLow;
      case 'medium':
        return priorityMedium;
      case 'high':
        return priorityHigh;
      case 'urgent':
        return priorityUrgent;
      default:
        return priorityMedium;
    }
  }

  static Color getSpecialtyColor(String specialty) {
    switch (specialty.toLowerCase()) {
      case 'physiotherapy':
        return physiotherapy;
      case 'prosthetics':
        return prosthetics;
      case 'orthopedics':
        return orthopedics;
      case 'neurology':
        return neurology;
      case 'rehabilitation':
        return rehabilitation;
      case 'occupationaltherapy':
      case 'occupational_therapy':
        return occupationalTherapy;
      case 'psychology':
        return psychology;
      case 'generalmedicine':
      case 'general_medicine':
        return generalMedicine;
      default:
        return grey500;
    }
  }

  // Material Color Swatches
  static const MaterialColor patientSwatch = MaterialColor(
    0xFF2196F3,
    <int, Color>{
      50: Color(0xFFE3F2FD),
      100: Color(0xFFBBDEFB),
      200: Color(0xFF90CAF9),
      300: Color(0xFF64B5F6),
      400: Color(0xFF42A5F5),
      500: Color(0xFF2196F3),
      600: Color(0xFF1E88E5),
      700: Color(0xFF1976D2),
      800: Color(0xFF1565C0),
      900: Color(0xFF0D47A1),
    },
  );

  static const MaterialColor doctorSwatch = MaterialColor(
    0xFF4CAF50,
    <int, Color>{
      50: Color(0xFFE8F5E8),
      100: Color(0xFFC8E6C9),
      200: Color(0xFFA5D6A7),
      300: Color(0xFF81C784),
      400: Color(0xFF66BB6A),
      500: Color(0xFF4CAF50),
      600: Color(0xFF43A047),
      700: Color(0xFF388E3C),
      800: Color(0xFF2E7D32),
      900: Color(0xFF1B5E20),
    },
  );

  static const MaterialColor adminSwatch = MaterialColor(
    0xFFF44336,
    <int, Color>{
      50: Color(0xFFFFEBEE),
      100: Color(0xFFFFCDD2),
      200: Color(0xFFEF9A9A),
      300: Color(0xFFE57373),
      400: Color(0xFFEF5350),
      500: Color(0xFFF44336),
      600: Color(0xFFE53935),
      700: Color(0xFFD32F2F),
      800: Color(0xFFC62828),
      900: Color(0xFFB71C1C),
    },
  );
}
