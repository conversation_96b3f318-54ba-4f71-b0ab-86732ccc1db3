import 'package:uuid/uuid.dart';

enum MessageType {
  text,
  image,
  document,
  video,
  audio,
  location,
  system,
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  read,
  failed,
}

class ChatMessage {
  final String id;
  final String chatId;
  final String senderId;
  final String? replyToMessageId;
  final MessageType type;
  final String content;
  final String? mediaUrl;
  final String? fileName;
  final int? fileSize;
  final MessageStatus status;
  final DateTime timestamp;
  final DateTime? editedAt;
  final bool isDeleted;
  final Map<String, dynamic>? metadata;

  ChatMessage({
    String? id,
    required this.chatId,
    required this.senderId,
    this.replyToMessageId,
    required this.type,
    required this.content,
    this.mediaUrl,
    this.fileName,
    this.fileSize,
    this.status = MessageStatus.sending,
    DateTime? timestamp,
    this.editedAt,
    this.isDeleted = false,
    this.metadata,
  })  : id = id ?? const Uuid().v4(),
        timestamp = timestamp ?? DateTime.now();

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'],
      chatId: json['chatId'],
      senderId: json['senderId'],
      replyToMessageId: json['replyToMessageId'],
      type: MessageType.values.firstWhere(
        (e) => e.toString() == 'MessageType.${json['type']}',
      ),
      content: json['content'],
      mediaUrl: json['mediaUrl'],
      fileName: json['fileName'],
      fileSize: json['fileSize'],
      status: MessageStatus.values.firstWhere(
        (e) => e.toString() == 'MessageStatus.${json['status']}',
      ),
      timestamp: DateTime.parse(json['timestamp']),
      editedAt: json['editedAt'] != null
          ? DateTime.parse(json['editedAt'])
          : null,
      isDeleted: json['isDeleted'] ?? false,
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chatId': chatId,
      'senderId': senderId,
      'replyToMessageId': replyToMessageId,
      'type': type.toString().split('.').last,
      'content': content,
      'mediaUrl': mediaUrl,
      'fileName': fileName,
      'fileSize': fileSize,
      'status': status.toString().split('.').last,
      'timestamp': timestamp.toIso8601String(),
      'editedAt': editedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'metadata': metadata,
    };
  }

  bool get isMediaMessage => [
        MessageType.image,
        MessageType.video,
        MessageType.audio,
        MessageType.document,
      ].contains(type);

  ChatMessage copyWith({
    String? chatId,
    String? senderId,
    String? replyToMessageId,
    MessageType? type,
    String? content,
    String? mediaUrl,
    String? fileName,
    int? fileSize,
    MessageStatus? status,
    DateTime? timestamp,
    DateTime? editedAt,
    bool? isDeleted,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: id,
      chatId: chatId ?? this.chatId,
      senderId: senderId ?? this.senderId,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      type: type ?? this.type,
      content: content ?? this.content,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      editedAt: editedAt ?? this.editedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatMessage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum ChatType {
  consultation,
  support,
  group,
}

enum ChatStatus {
  active,
  archived,
  blocked,
}

class Chat {
  final String id;
  final ChatType type;
  final String? consultationId; // if related to a consultation
  final List<String> participantIds;
  final String? title;
  final String? description;
  final String? lastMessageId;
  final DateTime? lastMessageAt;
  final Map<String, int> unreadCounts; // userId -> unread count
  final ChatStatus status;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;

  Chat({
    String? id,
    required this.type,
    this.consultationId,
    required this.participantIds,
    this.title,
    this.description,
    this.lastMessageId,
    this.lastMessageAt,
    this.unreadCounts = const {},
    this.status = ChatStatus.active,
    this.metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory Chat.fromJson(Map<String, dynamic> json) {
    return Chat(
      id: json['id'],
      type: ChatType.values.firstWhere(
        (e) => e.toString() == 'ChatType.${json['type']}',
      ),
      consultationId: json['consultationId'],
      participantIds: List<String>.from(json['participantIds']),
      title: json['title'],
      description: json['description'],
      lastMessageId: json['lastMessageId'],
      lastMessageAt: json['lastMessageAt'] != null
          ? DateTime.parse(json['lastMessageAt'])
          : null,
      unreadCounts: Map<String, int>.from(json['unreadCounts'] ?? {}),
      status: ChatStatus.values.firstWhere(
        (e) => e.toString() == 'ChatStatus.${json['status']}',
      ),
      metadata: json['metadata'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString().split('.').last,
      'consultationId': consultationId,
      'participantIds': participantIds,
      'title': title,
      'description': description,
      'lastMessageId': lastMessageId,
      'lastMessageAt': lastMessageAt?.toIso8601String(),
      'unreadCounts': unreadCounts,
      'status': status.toString().split('.').last,
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  int getUnreadCount(String userId) {
    return unreadCounts[userId] ?? 0;
  }

  bool hasUnreadMessages(String userId) {
    return getUnreadCount(userId) > 0;
  }

  Chat copyWith({
    ChatType? type,
    String? consultationId,
    List<String>? participantIds,
    String? title,
    String? description,
    String? lastMessageId,
    DateTime? lastMessageAt,
    Map<String, int>? unreadCounts,
    ChatStatus? status,
    Map<String, dynamic>? metadata,
    DateTime? updatedAt,
  }) {
    return Chat(
      id: id,
      type: type ?? this.type,
      consultationId: consultationId ?? this.consultationId,
      participantIds: participantIds ?? this.participantIds,
      title: title ?? this.title,
      description: description ?? this.description,
      lastMessageId: lastMessageId ?? this.lastMessageId,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      unreadCounts: unreadCounts ?? this.unreadCounts,
      status: status ?? this.status,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Chat && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Chat(id: $id, type: $type, participants: ${participantIds.length})';
  }
}
