import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';

enum ButtonSize {
  small,
  medium,
  large,
}

class GradientButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final LinearGradient? gradient;
  final ButtonSize size;
  final bool isLoading;
  final Widget? icon;
  final bool isOutlined;
  final Color? textColor;
  final double? width;
  final EdgeInsetsGeometry? padding;

  const GradientButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.gradient,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.icon,
    this.isOutlined = false,
    this.textColor,
    this.width,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final effectiveGradient = gradient ?? AppColors.buttonGradient;
    final isEnabled = onPressed != null && !isLoading;

    // Get size-specific properties
    final sizeProps = _getSizeProperties();

    Widget buttonChild = Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isLoading) ...[
          SizedBox(
            width: sizeProps.iconSize,
            height: sizeProps.iconSize,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                textColor ?? AppColors.white,
              ),
            ),
          ),
          const SizedBox(width: 8),
        ] else if (icon != null) ...[
          icon!,
          const SizedBox(width: 8),
        ],
        Text(
          text,
          style: sizeProps.textStyle.copyWith(
            color: textColor ?? AppColors.white,
          ),
        ),
      ],
    );

    Widget button = Container(
      width: width,
      height: sizeProps.height,
      decoration: BoxDecoration(
        gradient: isEnabled ? effectiveGradient : null,
        color: isEnabled ? null : AppColors.grey300,
        borderRadius: BorderRadius.circular(sizeProps.borderRadius),
        border: isOutlined
            ? Border.all(
                color: isEnabled ? effectiveGradient.colors.first : AppColors.grey300,
                width: 2,
              )
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled ? onPressed : null,
          borderRadius: BorderRadius.circular(sizeProps.borderRadius),
          child: Container(
            padding: padding ?? sizeProps.padding,
            child: buttonChild,
          ),
        ),
      ),
    );

    if (isOutlined) {
      button = Container(
        width: width,
        height: sizeProps.height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(sizeProps.borderRadius),
          border: Border.all(
            color: isEnabled ? effectiveGradient.colors.first : AppColors.grey300,
            width: 2,
          ),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: isEnabled ? onPressed : null,
            borderRadius: BorderRadius.circular(sizeProps.borderRadius),
            child: Container(
              padding: padding ?? sizeProps.padding,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (isLoading) ...[
                    SizedBox(
                      width: sizeProps.iconSize,
                      height: sizeProps.iconSize,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          effectiveGradient.colors.first,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                  ] else if (icon != null) ...[
                    icon!,
                    const SizedBox(width: 8),
                  ],
                  Text(
                    text,
                    style: sizeProps.textStyle.copyWith(
                      color: effectiveGradient.colors.first,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    return button;
  }

  _ButtonSizeProperties _getSizeProperties() {
    switch (size) {
      case ButtonSize.small:
        return _ButtonSizeProperties(
          height: 36,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          textStyle: AppTheme.buttonSmall,
          iconSize: 16,
          borderRadius: 8,
        );
      case ButtonSize.medium:
        return _ButtonSizeProperties(
          height: 44,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          textStyle: AppTheme.buttonMedium,
          iconSize: 20,
          borderRadius: 12,
        );
      case ButtonSize.large:
        return _ButtonSizeProperties(
          height: 52,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          textStyle: AppTheme.buttonLarge,
          iconSize: 24,
          borderRadius: 16,
        );
    }
  }
}

class _ButtonSizeProperties {
  final double height;
  final EdgeInsetsGeometry padding;
  final TextStyle textStyle;
  final double iconSize;
  final double borderRadius;

  _ButtonSizeProperties({
    required this.height,
    required this.padding,
    required this.textStyle,
    required this.iconSize,
    required this.borderRadius,
  });
}

// Specialized gradient buttons for different user types
class PatientButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonSize size;
  final bool isLoading;
  final Widget? icon;
  final bool isOutlined;
  final double? width;

  const PatientButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.icon,
    this.isOutlined = false,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GradientButton(
      text: text,
      onPressed: onPressed,
      gradient: AppColors.patientGradient,
      size: size,
      isLoading: isLoading,
      icon: icon,
      isOutlined: isOutlined,
      width: width,
    );
  }
}

class DoctorButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonSize size;
  final bool isLoading;
  final Widget? icon;
  final bool isOutlined;
  final double? width;

  const DoctorButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.icon,
    this.isOutlined = false,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GradientButton(
      text: text,
      onPressed: onPressed,
      gradient: AppColors.doctorGradient,
      size: size,
      isLoading: isLoading,
      icon: icon,
      isOutlined: isOutlined,
      width: width,
    );
  }
}

class AdminButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonSize size;
  final bool isLoading;
  final Widget? icon;
  final bool isOutlined;
  final double? width;

  const AdminButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.icon,
    this.isOutlined = false,
    this.width,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GradientButton(
      text: text,
      onPressed: onPressed,
      gradient: AppColors.adminGradient,
      size: size,
      isLoading: isLoading,
      icon: icon,
      isOutlined: isOutlined,
      width: width,
    );
  }
}
