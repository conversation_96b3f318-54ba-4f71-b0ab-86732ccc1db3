{"logs": [{"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-release-26:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,1108", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,1204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,148,243,344", "endColumns": "92,94,100,94", "endOffsets": "143,238,339,434"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "724,817,912,1013", "endColumns": "92,94,100,94", "endOffsets": "812,907,1008,1103"}}]}, {"outputFile": "io.flutter.plugins.urllauncher.url_launcher_android-mergeReleaseResources-24:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,1108", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,1204"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,148,243,344", "endColumns": "92,94,100,94", "endOffsets": "143,238,339,434"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "724,817,912,1013", "endColumns": "92,94,100,94", "endOffsets": "812,907,1008,1103"}}]}]}