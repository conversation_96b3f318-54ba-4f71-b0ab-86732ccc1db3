import 'package:flutter/material.dart';

class AppConstants {
  // App Information
  static const String appName = 'خطوة';
  static const String appNameEnglish = 'Khatwa';
  static const String appTagline = 'خطوة نحو حياة أفضل ومستقبل أعظم';
  static const String appTaglineEnglish = 'A step towards a better life and a greater future';
  static const String appVersion = '1.0.0';

  // API Configuration
  static const String baseUrl = 'https://api.khatwa.com';
  static const String apiVersion = 'v1';
  static const Duration apiTimeout = Duration(seconds: 30);

  // Database Configuration
  static const String databaseName = 'khatwa.db';
  static const int databaseVersion = 1;

  // Shared Preferences Keys
  static const String keyUserId = 'user_id';
  static const String keyUserType = 'user_type';
  static const String keyAuthToken = 'auth_token';
  static const String keyLanguage = 'language';
  static const String keyThemeMode = 'theme_mode';
  static const String keyFirstLaunch = 'first_launch';
  static const String keyNotificationsEnabled = 'notifications_enabled';

  // File Upload Configuration
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif'];
  static const List<String> allowedDocumentTypes = ['pdf', 'doc', 'docx', 'txt'];
  static const List<String> allowedVideoTypes = ['mp4', 'avi', 'mov'];

  // Chat Configuration
  static const int maxMessageLength = 1000;
  static const int messagesPerPage = 50;
  static const Duration typingIndicatorTimeout = Duration(seconds: 3);

  // Map Configuration
  static const double defaultLatitude = 24.7136; // Riyadh, Saudi Arabia
  static const double defaultLongitude = 46.6753;
  static const double defaultZoom = 12.0;
  static const double searchRadius = 50.0; // km

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  static const int maxBioLength = 500;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // Timeouts
  static const Duration splashScreenDuration = Duration(seconds: 3);
  static const Duration snackBarDuration = Duration(seconds: 4);
  static const Duration loadingTimeout = Duration(seconds: 30);

  // Regular Expressions
  static final RegExp emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );
  
  static final RegExp phoneRegex = RegExp(
    r'^[\+]?[1-9][\d]{0,15}$',
  );
  
  static final RegExp passwordRegex = RegExp(
    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$',
  );

  // Error Messages (Arabic)
  static const String errorGeneral = 'حدث خطأ غير متوقع';
  static const String errorNetwork = 'خطأ في الاتصال بالشبكة';
  static const String errorTimeout = 'انتهت مهلة الاتصال';
  static const String errorInvalidEmail = 'البريد الإلكتروني غير صحيح';
  static const String errorInvalidPhone = 'رقم الهاتف غير صحيح';
  static const String errorWeakPassword = 'كلمة المرور ضعيفة';
  static const String errorPasswordMismatch = 'كلمات المرور غير متطابقة';
  static const String errorRequiredField = 'هذا الحقل مطلوب';
  static const String errorFileTooLarge = 'حجم الملف كبير جداً';
  static const String errorUnsupportedFileType = 'نوع الملف غير مدعوم';

  // Success Messages (Arabic)
  static const String successRegistration = 'تم التسجيل بنجاح';
  static const String successLogin = 'تم تسجيل الدخول بنجاح';
  static const String successLogout = 'تم تسجيل الخروج بنجاح';
  static const String successProfileUpdate = 'تم تحديث الملف الشخصي بنجاح';
  static const String successPasswordChange = 'تم تغيير كلمة المرور بنجاح';
  static const String successMessageSent = 'تم إرسال الرسالة بنجاح';
  static const String successConsultationCreated = 'تم إنشاء الاستشارة بنجاح';

  // Loading Messages (Arabic)
  static const String loadingGeneral = 'جاري التحميل...';
  static const String loadingLogin = 'جاري تسجيل الدخول...';
  static const String loadingRegistration = 'جاري التسجيل...';
  static const String loadingProfile = 'جاري تحميل الملف الشخصي...';
  static const String loadingConsultations = 'جاري تحميل الاستشارات...';
  static const String loadingMessages = 'جاري تحميل الرسائل...';
  static const String loadingCenters = 'جاري تحميل المراكز...';

  // Empty State Messages (Arabic)
  static const String emptyConsultations = 'لا توجد استشارات';
  static const String emptyMessages = 'لا توجد رسائل';
  static const String emptyCenters = 'لا توجد مراكز قريبة';
  static const String emptyNotifications = 'لا توجد إشعارات';
  static const String emptySearch = 'لا توجد نتائج للبحث';

  // Button Labels (Arabic)
  static const String buttonLogin = 'تسجيل الدخول';
  static const String buttonRegister = 'التسجيل';
  static const String buttonSave = 'حفظ';
  static const String buttonCancel = 'إلغاء';
  static const String buttonDelete = 'حذف';
  static const String buttonEdit = 'تعديل';
  static const String buttonSend = 'إرسال';
  static const String buttonSearch = 'بحث';
  static const String buttonFilter = 'تصفية';
  static const String buttonRefresh = 'تحديث';
  static const String buttonRetry = 'إعادة المحاولة';
  static const String buttonContinue = 'متابعة';
  static const String buttonBack = 'رجوع';
  static const String buttonNext = 'التالي';
  static const String buttonFinish = 'إنهاء';
  static const String buttonClose = 'إغلاق';

  // Navigation Labels (Arabic)
  static const String navHome = 'الرئيسية';
  static const String navConsultations = 'الاستشارات';
  static const String navChat = 'المحادثات';
  static const String navCenters = 'المراكز';
  static const String navProfile = 'الملف الشخصي';
  static const String navSettings = 'الإعدادات';
  static const String navNotifications = 'الإشعارات';
  static const String navHelp = 'المساعدة';
  static const String navAbout = 'حول التطبيق';

  // User Types (Arabic)
  static const String userTypePatient = 'مريض';
  static const String userTypeDoctor = 'طبيب';
  static const String userTypeAdmin = 'مدير';

  // Consultation Status (Arabic)
  static const String consultationPending = 'في الانتظار';
  static const String consultationAccepted = 'مقبولة';
  static const String consultationInProgress = 'جارية';
  static const String consultationCompleted = 'مكتملة';
  static const String consultationCancelled = 'ملغية';
  static const String consultationRejected = 'مرفوضة';

  // Days of Week (Arabic)
  static const List<String> daysOfWeek = [
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
    'الجمعة',
    'السبت',
    'الأحد',
  ];

  // Months (Arabic)
  static const List<String> months = [
    'يناير',
    'فبراير',
    'مارس',
    'أبريل',
    'مايو',
    'يونيو',
    'يوليو',
    'أغسطس',
    'سبتمبر',
    'أكتوبر',
    'نوفمبر',
    'ديسمبر',
  ];

  // Disability Types (Arabic)
  static const Map<String, String> disabilityTypes = {
    'mobility': 'إعاقة حركية',
    'prosthetic': 'أطراف صناعية',
    'amputation': 'بتر',
    'spinalCord': 'إصابة الحبل الشوكي',
    'cerebralPalsy': 'الشلل الدماغي',
    'other': 'أخرى',
  };

  // Specializations (Arabic)
  static const Map<String, String> specializations = {
    'physiotherapy': 'العلاج الطبيعي',
    'prosthetics': 'الأطراف الصناعية',
    'orthopedics': 'جراحة العظام',
    'neurology': 'طب الأعصاب',
    'rehabilitation': 'التأهيل',
    'occupationalTherapy': 'العلاج الوظيفي',
    'psychology': 'علم النفس',
    'generalMedicine': 'الطب العام',
    'other': 'أخرى',
  };
}
