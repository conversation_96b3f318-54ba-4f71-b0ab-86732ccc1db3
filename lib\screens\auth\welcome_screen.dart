rimport 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';
import '../../widgets/common/gradient_button.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Gradient Background
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [AppColors.patientLight, AppColors.white],
              ),
            ),
          ),
          // Content
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  Expanded(
                    flex: 3,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // App Logo
                        Container(
                          width: 150,
                          height: 150,
                          decoration: BoxDecoration(
                            color: AppColors.white,
                            borderRadius: BorderRadius.circular(30),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.patientPrimary.withOpacity(0.2),
                                blurRadius: 20,
                                offset: const Offset(0, 10),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(30),
                            child: Image.asset(
                              'assets/images/logo.png',
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                        const SizedBox(height: 32),
                        // App Name
                        Text(
                          AppConstants.appName,
                          style: AppTheme.heading1.copyWith(
                            color: AppColors.patientPrimary,
                            fontFamily: AppTheme.decorativeFontFamily,
                          ),
                        ),
                        const SizedBox(height: 16),
                        // App Tagline
                        Text(
                          AppConstants.appTagline,
                          style: AppTheme.bodyLarge.copyWith(
                            color: AppColors.textSecondary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 32),
                        // Description
                        Text(
                          'منصة شاملة تربط بين المرضى والمختصين لتقديم أفضل الخدمات الاستشارية والعلاجية',
                          style: AppTheme.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // User Type Selection
                        Text(
                          'اختر نوع حسابك',
                          style: AppTheme.heading5.copyWith(
                            color: AppColors.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 24),
                        // Patient Button
                        PatientButton(
                          text: 'مريض',
                          onPressed: () {
                            Navigator.pushNamed(context, '/choose-account');
                          },
                          size: ButtonSize.large,
                          width: double.infinity,
                          icon: const Icon(
                            Icons.person,
                            color: AppColors.white,
                          ),
                        ),
                        const SizedBox(height: 16),
                        // Doctor Button
                        DoctorButton(
                          text: 'طبيب / مختص',
                          onPressed: () {
                            Navigator.pushNamed(context, '/choose-account');
                          },
                          size: ButtonSize.large,
                          width: double.infinity,
                          icon: const Icon(
                            Icons.medical_services,
                            color: AppColors.white,
                          ),
                        ),
                        const SizedBox(height: 16),
                        // Admin Button
                        AdminButton(
                          text: 'إدارة',
                          onPressed: () {
                            Navigator.pushNamed(context, '/login');
                          },
                          size: ButtonSize.large,
                          width: double.infinity,
                          icon: const Icon(
                            Icons.admin_panel_settings,
                            color: AppColors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Footer
                  Padding(
                    padding: const EdgeInsets.only(top: 16),
                    child: Column(
                      children: [
                        Text(
                          'الإصدار ${AppConstants.appVersion}',
                          style: AppTheme.caption,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '© 2024 خطوة. جميع الحقوق محفوظة',
                          style: AppTheme.caption,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
