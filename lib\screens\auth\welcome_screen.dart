import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';
import '../../widgets/common/gradient_button.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  VideoPlayerController? _videoController;
  bool _hasVideoAsset = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void dispose() {
    _videoController?.dispose();
    super.dispose();
  }

  Future<void> _initializeVideo() async {
    try {
      _videoController = VideoPlayerController.asset(
        'assets/videos/welcome.mp4',
      );
      await _videoController!.initialize();
      _videoController!.setLooping(true);
      _videoController!.setVolume(0.0); // Mute the video
      _videoController!.play();
      setState(() {
        _hasVideoAsset = true;
      });
    } catch (e) {
      // Video asset not found, use gradient background instead
      setState(() {
        _hasVideoAsset = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Video Background or Gradient Fallback
          if (_hasVideoAsset && _videoController != null && _videoController!.value.isInitialized)
            Positioned.fill(
              child: FittedBox(
                fit: BoxFit.cover,
                child: SizedBox(
                  width: _videoController!.value.size.width,
                  height: _videoController!.value.size.height,
                  child: VideoPlayer(_videoController!),
                ),
              ),
            )
          else
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [AppColors.patientLight, AppColors.white],
                ),
              ),
            ),

          // Dark Overlay for better text readability when video is playing
          if (_hasVideoAsset && _videoController != null && _videoController!.value.isInitialized)
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withOpacity(0.3),
                    Colors.black.withOpacity(0.5),
                  ],
                ),
              ),
            ),

          // Content
          SafeArea(
            child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              children: [
                Expanded(
                  flex: 3,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // App Logo
                      Container(
                        width: 150,
                        height: 150,
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.circular(30),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.patientPrimary.withOpacity(0.2),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.accessibility_new,
                          size: 80,
                          color: AppColors.patientPrimary,
                        ),
                      ),
                      const SizedBox(height: 32),

                      // App Name
                      Text(
                        AppConstants.appName,
                        style: AppTheme.heading1.copyWith(
                          color: AppColors.patientPrimary,
                          fontFamily: AppTheme.decorativeFontFamily,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // App Tagline
                      Text(
                        AppConstants.appTagline,
                        style: AppTheme.bodyLarge.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),

                      // Description
                      Text(
                        'منصة شاملة تربط بين المرضى والمختصين لتقديم أفضل الخدمات الاستشارية والعلاجية',
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                Expanded(
                  flex: 2,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // User Type Selection
                      Text(
                        'اختر نوع حسابك',
                        style: AppTheme.heading5.copyWith(
                          color: AppColors.textPrimary,
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Patient Button
                      PatientButton(
                        text: 'مريض',
                        onPressed: () {
                          Navigator.pushNamed(context, '/choose-account');
                        },
                        size: ButtonSize.large,
                        width: double.infinity,
                        icon: const Icon(Icons.person, color: AppColors.white),
                      ),
                      const SizedBox(height: 16),

                      // Doctor Button
                      DoctorButton(
                        text: 'طبيب / مختص',
                        onPressed: () {
                          Navigator.pushNamed(context, '/choose-account');
                        },
                        size: ButtonSize.large,
                        width: double.infinity,
                        icon: const Icon(
                          Icons.medical_services,
                          color: AppColors.white,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Admin Button
                      AdminButton(
                        text: 'إدارة',
                        onPressed: () {
                          Navigator.pushNamed(context, '/login');
                        },
                        size: ButtonSize.large,
                        width: double.infinity,
                        icon: const Icon(
                          Icons.admin_panel_settings,
                          color: AppColors.white,
                        ),
                      ),
                    ],
                  ),
                ),

                // Footer
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: Column(
                    children: [
                      Text(
                        'الإصدار ${AppConstants.appVersion}',
                        style: AppTheme.caption,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '© 2024 خطوة. جميع الحقوق محفوظة',
                        style: AppTheme.caption,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }


}
