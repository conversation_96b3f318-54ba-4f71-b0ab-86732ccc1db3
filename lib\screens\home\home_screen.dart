import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/common/gradient_button.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              AppConstants.appName,
              style: AppTheme.heading5.copyWith(
                fontFamily: AppTheme.decorativeFontFamily,
              ),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.notifications_outlined),
                onPressed: () {
                  _showComingSoon(context, 'الإشعارات');
                },
              ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'profile':
                      _showComingSoon(context, 'الملف الشخصي');
                      break;
                    case 'settings':
                      _showComingSoon(context, 'الإعدادات');
                      break;
                    case 'logout':
                      _showLogoutDialog(context, authProvider);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'profile',
                    child: ListTile(
                      leading: Icon(Icons.person_outline),
                      title: Text('الملف الشخصي'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'settings',
                    child: ListTile(
                      leading: Icon(Icons.settings_outlined),
                      title: Text('الإعدادات'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuDivider(),
                  const PopupMenuItem(
                    value: 'logout',
                    child: ListTile(
                      leading: Icon(Icons.logout, color: AppColors.error),
                      title: Text('تسجيل الخروج', style: TextStyle(color: AppColors.error)),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ],
          ),
          body: _buildBody(authProvider),
          bottomNavigationBar: _buildBottomNavigationBar(),
        );
      },
    );
  }

  Widget _buildBody(AuthProvider authProvider) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: AppColors.getUserTypeColor(
                          authProvider.userType?.toString().split('.').last ?? 'patient',
                        ),
                        child: Text(
                          authProvider.user?.fullName.substring(0, 1) ?? 'م',
                          style: AppTheme.heading4.copyWith(color: AppColors.white),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'مرحباً، ${authProvider.user?.fullName ?? 'المستخدم'}',
                              style: AppTheme.heading5,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              authProvider.getUserRoleDisplayName(),
                              style: AppTheme.bodyMedium.copyWith(
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'نحن هنا لمساعدتك في رحلتك نحو حياة أفضل',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Quick Actions
          Text(
            'الإجراءات السريعة',
            style: AppTheme.heading5,
          ),
          const SizedBox(height: 16),
          
          _buildQuickActions(authProvider),
          
          const SizedBox(height: 24),
          
          // Recent Activity
          Text(
            'النشاط الأخير',
            style: AppTheme.heading5,
          ),
          const SizedBox(height: 16),
          
          _buildRecentActivity(),
        ],
      ),
    );
  }

  Widget _buildQuickActions(AuthProvider authProvider) {
    final userType = authProvider.userType?.toString().split('.').last ?? 'patient';
    
    List<_QuickAction> actions = [];
    
    switch (userType) {
      case 'patient':
        actions = [
          _QuickAction(
            icon: Icons.medical_services,
            title: 'استشارة جديدة',
            subtitle: 'احجز استشارة مع مختص',
            color: AppColors.patientPrimary,
            onTap: () => _showComingSoon(context, 'الاستشارات'),
          ),
          _QuickAction(
            icon: Icons.chat,
            title: 'المحادثات',
            subtitle: 'تواصل مع الأطباء',
            color: AppColors.info,
            onTap: () => _showComingSoon(context, 'المحادثات'),
          ),
          _QuickAction(
            icon: Icons.location_on,
            title: 'المراكز القريبة',
            subtitle: 'ابحث عن مراكز قريبة',
            color: AppColors.success,
            onTap: () => _showComingSoon(context, 'المراكز الطبية'),
          ),
          _QuickAction(
            icon: Icons.folder_shared,
            title: 'ملفي الطبي',
            subtitle: 'عرض وإدارة ملفك الطبي',
            color: AppColors.warning,
            onTap: () => _showComingSoon(context, 'الملف الطبي'),
          ),
        ];
        break;
      case 'doctor':
        actions = [
          _QuickAction(
            icon: Icons.assignment,
            title: 'الاستشارات',
            subtitle: 'إدارة طلبات الاستشارة',
            color: AppColors.doctorPrimary,
            onTap: () => _showComingSoon(context, 'إدارة الاستشارات'),
          ),
          _QuickAction(
            icon: Icons.chat,
            title: 'المحادثات',
            subtitle: 'التواصل مع المرضى',
            color: AppColors.info,
            onTap: () => _showComingSoon(context, 'المحادثات'),
          ),
          _QuickAction(
            icon: Icons.schedule,
            title: 'جدولي',
            subtitle: 'إدارة المواعيد',
            color: AppColors.warning,
            onTap: () => _showComingSoon(context, 'الجدول'),
          ),
          _QuickAction(
            icon: Icons.analytics,
            title: 'الإحصائيات',
            subtitle: 'عرض إحصائيات الأداء',
            color: AppColors.success,
            onTap: () => _showComingSoon(context, 'الإحصائيات'),
          ),
        ];
        break;
      case 'admin':
        actions = [
          _QuickAction(
            icon: Icons.people,
            title: 'إدارة المستخدمين',
            subtitle: 'إدارة حسابات المستخدمين',
            color: AppColors.adminPrimary,
            onTap: () => _showComingSoon(context, 'إدارة المستخدمين'),
          ),
          _QuickAction(
            icon: Icons.verified_user,
            title: 'التحقق من الأطباء',
            subtitle: 'مراجعة طلبات التحقق',
            color: AppColors.success,
            onTap: () => _showComingSoon(context, 'التحقق من الأطباء'),
          ),
          _QuickAction(
            icon: Icons.report,
            title: 'التقارير',
            subtitle: 'عرض تقارير النظام',
            color: AppColors.info,
            onTap: () => _showComingSoon(context, 'التقارير'),
          ),
          _QuickAction(
            icon: Icons.settings,
            title: 'إعدادات النظام',
            subtitle: 'إدارة إعدادات التطبيق',
            color: AppColors.warning,
            onTap: () => _showComingSoon(context, 'إعدادات النظام'),
          ),
        ];
        break;
    }
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.2,
      ),
      itemCount: actions.length,
      itemBuilder: (context, index) {
        final action = actions[index];
        return Card(
          child: InkWell(
            onTap: action.onTap,
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    action.icon,
                    size: 32,
                    color: action.color,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    action.title,
                    style: AppTheme.labelLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    action.subtitle,
                    style: AppTheme.caption,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentActivity() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              Icons.history,
              size: 48,
              color: AppColors.grey400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا يوجد نشاط حديث',
              style: AppTheme.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ستظهر هنا أحدث أنشطتك في التطبيق',
              style: AppTheme.caption,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
        });
        if (index != 0) {
          _showComingSoon(context, 'هذا القسم');
        }
      },
      type: BottomNavigationBarType.fixed,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: AppConstants.navHome,
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.medical_services),
          label: AppConstants.navConsultations,
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.chat),
          label: AppConstants.navChat,
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.location_on),
          label: AppConstants.navCenters,
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: AppConstants.navProfile,
        ),
      ],
    );
  }

  void _showComingSoon(BuildContext context, String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('ميزة $feature ستكون متاحة قريباً'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, AuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await authProvider.logout();
              if (context.mounted) {
                Navigator.of(context).pushReplacementNamed('/welcome');
              }
            },
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}

class _QuickAction {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback onTap;

  _QuickAction({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.onTap,
  });
}
