import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../models/patient_model.dart';
import '../models/doctor_model.dart';
import '../constants/app_constants.dart';
import 'database_service.dart';

class AuthResult {
  final bool success;
  final String? message;
  final User? user;

  AuthResult({
    required this.success,
    this.message,
    this.user,
  });
}

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final DatabaseService _databaseService = DatabaseService();
  User? _currentUser;
  SharedPreferences? _prefs;

  // Getters
  User? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;
  UserType? get currentUserType => _currentUser?.userType;

  // Initialize the service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadCurrentUser();
  }

  // Load current user from storage
  Future<void> _loadCurrentUser() async {
    if (_prefs == null) return;

    final userId = _prefs!.getString(AppConstants.keyUserId);
    if (userId != null) {
      _currentUser = await _databaseService.getUserById(userId);
    }
  }

  // Save current user to storage
  Future<void> _saveCurrentUser(User user) async {
    if (_prefs == null) return;

    await _prefs!.setString(AppConstants.keyUserId, user.id);
    await _prefs!.setString(AppConstants.keyUserType, user.userType.toString().split('.').last);
    _currentUser = user;
  }

  // Clear current user from storage
  Future<void> _clearCurrentUser() async {
    if (_prefs == null) return;

    await _prefs!.remove(AppConstants.keyUserId);
    await _prefs!.remove(AppConstants.keyUserType);
    await _prefs!.remove(AppConstants.keyAuthToken);
    _currentUser = null;
  }

  // Hash password
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Validate email format
  bool _isValidEmail(String email) {
    return AppConstants.emailRegex.hasMatch(email);
  }

  // Validate phone format
  bool _isValidPhone(String phone) {
    return AppConstants.phoneRegex.hasMatch(phone);
  }

  // Validate password strength
  bool _isValidPassword(String password) {
    return password.length >= AppConstants.minPasswordLength &&
           password.length <= AppConstants.maxPasswordLength &&
           AppConstants.passwordRegex.hasMatch(password);
  }

  // Register new user
  Future<AuthResult> register({
    required String email,
    required String password,
    required String fullName,
    required UserType userType,
    String? phoneNumber,
  }) async {
    try {
      // Validate input
      if (!_isValidEmail(email)) {
        return AuthResult(
          success: false,
          message: AppConstants.errorInvalidEmail,
        );
      }

      if (!_isValidPassword(password)) {
        return AuthResult(
          success: false,
          message: AppConstants.errorWeakPassword,
        );
      }

      if (phoneNumber != null && !_isValidPhone(phoneNumber)) {
        return AuthResult(
          success: false,
          message: AppConstants.errorInvalidPhone,
        );
      }

      if (fullName.length < AppConstants.minNameLength || 
          fullName.length > AppConstants.maxNameLength) {
        return AuthResult(
          success: false,
          message: AppConstants.errorRequiredField,
        );
      }

      // Check if user already exists
      final existingUser = await _databaseService.getUserByEmail(email);
      if (existingUser != null) {
        return AuthResult(
          success: false,
          message: 'المستخدم موجود بالفعل',
        );
      }

      // Create new user
      final user = User(
        email: email,
        fullName: fullName,
        phoneNumber: phoneNumber,
        userType: userType,
        status: userType == UserType.doctor ? UserStatus.pending : UserStatus.active,
        additionalData: {
          'passwordHash': _hashPassword(password),
        },
      );

      // Save user to database
      await _databaseService.insertUser(user);

      // Save current user
      await _saveCurrentUser(user);

      return AuthResult(
        success: true,
        message: AppConstants.successRegistration,
        user: user,
      );
    } catch (e) {
      return AuthResult(
        success: false,
        message: AppConstants.errorGeneral,
      );
    }
  }

  // Login user
  Future<AuthResult> login({
    required String email,
    required String password,
  }) async {
    try {
      // Validate input
      if (!_isValidEmail(email)) {
        return AuthResult(
          success: false,
          message: AppConstants.errorInvalidEmail,
        );
      }

      // Find user by email
      final user = await _databaseService.getUserByEmail(email);
      if (user == null) {
        return AuthResult(
          success: false,
          message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
        );
      }

      // Check password
      final hashedPassword = _hashPassword(password);
      final storedHash = user.additionalData?['passwordHash'];
      
      if (storedHash != hashedPassword) {
        return AuthResult(
          success: false,
          message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
        );
      }

      // Check user status
      if (user.status == UserStatus.suspended) {
        return AuthResult(
          success: false,
          message: 'تم تعليق حسابك. يرجى التواصل مع الإدارة',
        );
      }

      if (user.status == UserStatus.inactive) {
        return AuthResult(
          success: false,
          message: 'حسابك غير مفعل',
        );
      }

      // Save current user
      await _saveCurrentUser(user);

      return AuthResult(
        success: true,
        message: AppConstants.successLogin,
        user: user,
      );
    } catch (e) {
      return AuthResult(
        success: false,
        message: AppConstants.errorGeneral,
      );
    }
  }

  // Logout user
  Future<void> logout() async {
    await _clearCurrentUser();
    await _databaseService.clearAllData();
  }

  // Change password
  Future<AuthResult> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      if (_currentUser == null) {
        return AuthResult(
          success: false,
          message: 'يجب تسجيل الدخول أولاً',
        );
      }

      // Validate new password
      if (!_isValidPassword(newPassword)) {
        return AuthResult(
          success: false,
          message: AppConstants.errorWeakPassword,
        );
      }

      // Check current password
      final hashedCurrentPassword = _hashPassword(currentPassword);
      final storedHash = _currentUser!.additionalData?['passwordHash'];
      
      if (storedHash != hashedCurrentPassword) {
        return AuthResult(
          success: false,
          message: 'كلمة المرور الحالية غير صحيحة',
        );
      }

      // Update password
      final updatedUser = _currentUser!.copyWith(
        additionalData: {
          ..._currentUser!.additionalData ?? {},
          'passwordHash': _hashPassword(newPassword),
        },
      );

      await _databaseService.update(
        'users',
        updatedUser.toJson(),
        where: 'id = ?',
        whereArgs: [updatedUser.id],
      );

      _currentUser = updatedUser;

      return AuthResult(
        success: true,
        message: AppConstants.successPasswordChange,
        user: updatedUser,
      );
    } catch (e) {
      return AuthResult(
        success: false,
        message: AppConstants.errorGeneral,
      );
    }
  }

  // Update user profile
  Future<AuthResult> updateProfile({
    String? fullName,
    String? phoneNumber,
    String? profileImageUrl,
  }) async {
    try {
      if (_currentUser == null) {
        return AuthResult(
          success: false,
          message: 'يجب تسجيل الدخول أولاً',
        );
      }

      // Validate input
      if (fullName != null && 
          (fullName.length < AppConstants.minNameLength || 
           fullName.length > AppConstants.maxNameLength)) {
        return AuthResult(
          success: false,
          message: AppConstants.errorRequiredField,
        );
      }

      if (phoneNumber != null && !_isValidPhone(phoneNumber)) {
        return AuthResult(
          success: false,
          message: AppConstants.errorInvalidPhone,
        );
      }

      // Update user
      final updatedUser = _currentUser!.copyWith(
        fullName: fullName,
        phoneNumber: phoneNumber,
        profileImageUrl: profileImageUrl,
      );

      await _databaseService.update(
        'users',
        updatedUser.toJson(),
        where: 'id = ?',
        whereArgs: [updatedUser.id],
      );

      _currentUser = updatedUser;

      return AuthResult(
        success: true,
        message: AppConstants.successProfileUpdate,
        user: updatedUser,
      );
    } catch (e) {
      return AuthResult(
        success: false,
        message: AppConstants.errorGeneral,
      );
    }
  }

  // Check if user has permission for specific action
  bool hasPermission(String action) {
    if (_currentUser == null) return false;

    switch (_currentUser!.userType) {
      case UserType.admin:
        return true; // Admin has all permissions
      case UserType.doctor:
        return [
          'view_consultations',
          'accept_consultations',
          'chat_with_patients',
          'update_profile',
          'view_medical_centers',
        ].contains(action);
      case UserType.patient:
        return [
          'create_consultations',
          'chat_with_doctors',
          'update_profile',
          'view_medical_centers',
          'rate_doctors',
        ].contains(action);
      default:
        return false;
    }
  }

  // Get user role display name
  String getUserRoleDisplayName() {
    if (_currentUser == null) return '';

    switch (_currentUser!.userType) {
      case UserType.patient:
        return AppConstants.userTypePatient;
      case UserType.doctor:
        return AppConstants.userTypeDoctor;
      case UserType.admin:
        return AppConstants.userTypeAdmin;
      default:
        return '';
    }
  }

  // Check if current user is verified (for doctors)
  Future<bool> isCurrentUserVerified() async {
    if (_currentUser == null || _currentUser!.userType != UserType.doctor) {
      return true; // Non-doctors are considered verified
    }

    final doctor = await _databaseService.getDoctorByUserId(_currentUser!.id);
    return doctor?.isVerified ?? false;
  }
}
